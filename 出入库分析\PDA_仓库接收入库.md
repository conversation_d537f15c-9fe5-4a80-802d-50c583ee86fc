```sql
-- DROP FUNCTION public.af_pda_wms_prod_inbound(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_inbound(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（本厂，分厂及委外厂产品）
 * 描述：  
 * 时间：  2024/07
 * 开发者：
 * 数据：  
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_sn_no text;
		_inbound_org text;
		json_result json;
		
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;		
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';		
		_sn_no := json_datas->>'sn_no';	
		
		--insert into a_test_log values(datas,'wms_prod_inbound',localtimestamp);
		
		--select me_finish_io_rmk1 into _inbound_org from me_finish_io_h where me_finish_io_no=_bill_no;
	
		select (case when workshop_no='J129' then 'JXML' when workshop_no='J171' then 'JXRS' else workshop_no end) into _inbound_org 
		from me_finish_io_h where me_finish_io_no=_bill_no;
	
		if coalesce(_inbound_org, '')='' then
			res := row('false', '此入库单没有指定入库组织，不能入库');
			return to_json(res);
		end if;
		
		if strpos(_inbound_org,'JX')=1 then
			json_result := af_pda_wms_prod_temp_receipt(json_build_object('user_no',_user_no,'bill_no',_bill_no,'sn_no',_sn_no)::text);
		else
			json_result := af_pda_wms_prod_factory_inbound(json_build_object('user_no',_user_no,'bill_no',_bill_no,'sn_no',_sn_no)::text);
		end if;
		
		res := row(json_result->>'successful', json_result->>'msg');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;

```


功能逻辑：


好的，我们来深入分析 `PDA_仓库接收入库.md` 的功能。基于对它调用的三个核心子函数（`af_pda_wms_prod_factory_inbound`, `af_pda_wms_prod_temp_receipt`, `af_ax_wms_temp_receipt_push_qm_si_lot`）的理解，我们可以对这个总入口进行一次全面的、升级版的分析。

## PDA仓库接收入库 (有序列号管控) 升级版功能分析

**函数名**: `af_pda_wms_prod_inbound`

### 1. 业务逻辑分析

**功能目标**: 作为一个**智能调度中心 (Dispatcher)**，统一处理所有**有序列号管控**的成品的入库请求。它的核心职责是精确识别产品来源（本厂 vs. 分厂/委外），然后将入库任务分派给对应的、专业的处理流程，最终向前端返回标准化的处理结果。

**核心业务流程**:

~~~mermaid
```mermaid
graph TD
    subgraph PDA端
        A[扫描: <br/> bill_no, sn_no, user_no]
    end
    
subgraph MES系统 - 总调度入口
    A --> B{af_pda_wms_prod_inbound}
    B --> C{读取 me_finish_io_h <br>获取 workshop_no}
    C --> D{判定组织是否为'JX'开头?}
end

subgraph "分支一: 江西/分厂产品 (workshop_no like 'JX%')"
    D -- 是 --> E[调用: af_pda_wms_prod_temp_receipt <br/>(暂收流程)]
    E --> F[1. 创建/更新 wm_temp_receipt (暂收单)]
    F --> G[2. 更新 wm_sn 状态: '110' -> '840'(暂收)]
    G --> H[3. 更新 me_finish_io 状态: '暂收完成']
    H --> I{暂收单是否满货?}
    I -- 是 --> J[调用: af_ax_wms_temp_receipt_push_qm_si_lot <br/>(下推质检)]
    J --> K[1. 创建 qm_si_lot_h (复检-检验批)]
    K --> L[2. 关联所有SN到 qm_si_lot_b_sn]
    L --> M[3. 更新 wm_temp_receipt 状态: '下推检验单完成']
    I -- 否 --> O[结束]
    M --> O
end

subgraph "分支二: 本厂产品"
    D -- 否 --> P[调用: af_pda_wms_prod_factory_inbound <br/>(直接入库流程)]
    P --> Q[1. 校验质检结论、SN状态等]
    Q --> R[2. 更新 wm_sn 状态: '110' -> '800'(在库)]
    R --> S[3. 更新 me_finish_io 状态: '入库完成']
    S --> T[结束]
end

subgraph 统一返回
    O & T --> U{统一格式化子函数返回的JSON}
    U --> V[返回结果给PDA]
end
~~~
```sql

**关键业务规则**:
1.  **统一入口，简化前端**: PDA端无需关心复杂的后台逻辑，只需调用这一个接口。
2.  **来源决定流程**: 产品的车间编号（`workshop_no`）是决定其走"直接入库"还是"暂收-复检-入库"流程的唯一依据。
3.  **职责分离 (分而治之)**: 总入口函数自身不处理任何复杂的业务校验或数据写入，它只做"判断"和"分发"这两件事，将具体的执行逻辑完全委托给专业的子函数，使得代码结构清晰、易于维护。

### 2. 代码逻辑分析 (SQL函数)

#### a. 参数与变量
*   **输入**: `datas` (JSON字符串)，包含 `user_no`, `bill_no`, `sn_no`。
*   **关键变量**: `_inbound_org` (用于存储判断来源的组织代码), `json_result` (用于捕获子函数的执行结果)。

#### b. 核心处理逻辑
1.  **解析组织 (Determine Origin)**:
    ```sql
    select (case when workshop_no='J129' then 'JXML' 
                 when workshop_no='J171' then 'JXRS' 
                 else workshop_no end) into _inbound_org 
    from me_finish_io_h where me_finish_io_no=_bill_no;
```
```sql
*   从源入库单头表 `me_finish_io_h` 中获取车间代码 `workshop_no`。
*   通过一个 `CASE` 语句，将特定的车间代码（如'J129'）映射为更具业务含义的组织代码（'JXML'），增加了可读性。
```

2.  **逻辑分发 (Dispatch)**:
    ```sql
    if strpos(_inbound_org,'JX')=1 then
        json_result := af_pda_wms_prod_temp_receipt(...);
    else
        json_result := af_pda_wms_prod_factory_inbound(...);
    end if;
    ```
    *   这是函数的心脏。使用 `strpos` 函数检查组织代码是否以 'JX' 开头（`strpos`返回1表示从第一个字符开始匹配）。
    *   如果是，则调用**暂收函数** `af_pda_wms_prod_temp_receipt`。
    *   否则，调用**本厂直接入库函数** `af_pda_wms_prod_factory_inbound`。

3.  **结果封装 (Standardize Response)**:
    ```sql
    res := row(json_result->>'successful', json_result->>'msg');
    return to_json(res);
    ```
    *   它不关心子函数内部复杂的处理过程，只作为"传话筒"，将子函数返回的JSON结果中的 `successful` 和 `msg` 字段提取出来，重新封装成标准的 `returntype` 格式返回给最前端的调用者。

### 3. 数据逻辑分析

#### 输入数据结构
```json
{
  "user_no": "PDA操作员编号",
  "datas": [{
    "bill_no": "完工单号",
    "sn_no": "扫描的产品外箱序列号" 
  }]
}
```

#### 核心数据操作
*   **查询**: 此函数本身**只读取** `me_finish_io_h` 表的一条记录来获取 `workshop_no`。
*   **更新/插入**: **无**。此函数自身是**只读 (Read-Only)** 的。所有的数据写入、更新、删除操作都由它所调用的子函数（`af_pda_wms_prod_temp_receipt` 和 `af_pda_wms_prod_factory_inbound`）完成。

### 4. 表之间关联逻辑 (全景图)

```mermaid
graph TD
    subgraph "af_pda_wms_prod_inbound (本函数)"
        A[PDA 输入] --> B(me_finish_io_h);
        B -- workshop_no --> C{判定来源};
    end

    subgraph "af_pda_wms_prod_factory_inbound (本厂流程)"
        C -- 非'JX' --> D_Read(pd_part);
        C -- 非'JX' --> E_Read(qm_si_lot_h/b_sn);
        C -- 非'JX' --> F_Read_Write(wm_sn);
        C -- 非'JX' --> G_Read_Write(me_finish_io/h);
    end

    subgraph "af_pda_wms_prod_temp_receipt (暂收流程)"
        C -- 'JX' --> H_Read(qm_si_lot_h);
        C -- 'JX' --> I_Write(wm_temp_receipt);
        C -- 'JX' --> J_Write(wm_temp_receipt_b);
        C -- 'JX' --> K_Write(wm_temp_receipt_sn_part);
        C -- 'JX' --> L_Read_Write(wm_sn);
        C -- 'JX' --> M_Read_Write(me_finish_io/h);
        I --> N{满货?};
    end
    
    subgraph "af_ax_wms_temp_receipt_push_qm_si_lot (下推质检)"
        N -- 是 --> O_Write(qm_si_lot_h)
        N -- 是 --> P_Write(qm_si_lot_b_sn)
    end

    style F_Read_Write fill:#f9f,stroke:#333,stroke-width:2px
    style G_Read_Write fill:#f9f,stroke:#333,stroke-width:2px
    style I_Write fill:#f96,stroke:#333,stroke-width:2px
    style J_Write fill:#f96,stroke:#333,stroke-width:2px
    style K_Write fill:#f96,stroke:#333,stroke-width:2px
    style L_Read_Write fill:#f9f,stroke:#333,stroke-width:2px
    style M_Read_Write fill:#f9f,stroke:#333,stroke-width:2px
    style O_Write fill:#f96,stroke:#333,stroke-width:2px
    style P_Write fill:#f96,stroke:#333,stroke-width:2px
```
*   此函数是整个复杂流程的"触发器"，它通过简单的读操作来决定启动哪个写操作密集型的子流程。

---

## 对比分析：有序列号 VS 无序列号

| 对比维度 | `af_pda_wms_prod_inbound` (有序列号) | `af_pda_wms_prod_inbound_no_sn` (无序列号) |
| :--- | :--- | :--- |
| **核心职责** | **智能调度器 (Dispatcher)** | **专一执行器 (Executor)** |
| **输入参数** | 必须包含 `sn_no` | **绝不包含** `sn_no` |
| **业务复杂度** | **极高**：通过分流处理两种业务模式，其中一种还包含下游触发逻辑。 | **低**：流程简化为单一、线性的路径。 |
| **代码逻辑** | 简单：核心是 `if-else` 分支和函数调用。 | 相对复杂：自身包含了多项前置条件校验和直接的状态更新逻辑。 |
| **数据操作** | **自身只读 (Read-Only)** | **自身读写 (Read & Write)**，直接更新单据状态。 |
| **流程路径** | **分叉型**：本厂(一步到位) vs 分厂(多步环节)。 | **直线型**：无论来源，均为"检验->更新状态"。 |
| **适用对象** | 序列号管控的高价值、需单件追溯的产品。 | 批次管控的低价值、大批量物料。 |

### 核心区别总结

1.  **角色定位的差异**：
    *   **有序列号**的函数是"指挥官"，它高屋建瓴，负责决策和分派任务。
    *   **无序列号**的函数是"突击兵"，它接收命令后，独立完成从校验到执行的全过程。

2.  **流程深度的差异**：
    *   有序列号的流程是**立体和纵深**的，它不仅处理当前入库，还会启动下游的"暂收"或"二次质检"等新流程。
    *   无序列号的流程是**扁平和完结**的，它的执行就代表着该业务环节的彻底结束。

这种清晰的设计模式，体现了优秀的软件工程实践：将**通用决策逻辑（判断来源）**上浮到入口层，将**特定执行逻辑（如何入库）**下沉到独立的、可复用的模块中，从而构建了一个健壮、可扩展的仓储管理核心系统。