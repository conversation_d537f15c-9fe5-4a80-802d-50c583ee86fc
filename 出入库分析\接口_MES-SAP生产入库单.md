
```sql
select ma.me_finish_io_no,
case when strpos(ma.mo_no,'F')=0 then
json_build_object('DocDate',to_char(ma.finish_io_datetime,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
json_agg(json_build_object('BaseType',202,'BaseRef',ma.mo_no,'WhsCode',mc.invp_area_no,'QuanTity',finish_io_qty_ok,'BatchNo',lot_no,'AcctCode',mb.acct_code,'U_BaseType','FQCTEMP','U_QtDocNum',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_QtLine',1)))
else json_build_object('DocDate',to_char(ma.finish_io_datetime,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
json_agg(json_build_object('ItemCode',ma.part_no,'WhsCode',mc.invp_area_no,'QuanTity',ma.finish_io_qty_ok,'BatchNo',ma.lot_no,'AcctCode',mb.acct_code,'Price',mb.price,'U_BaseType','SPLIT1','U_OPLANNUM',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_Reqline','1','U_QtDocNum',replace(ma.mo_no,'F',''),'U_QtLine',1))) end  as datas
from me_finish_io_h ma
left join mo mb on mb.mo_no=ma.mo_no
left join pd_part mc on mc.part_no=ma.part_no
where ma.me_finish_io_rmk4='入库完成' and coalesce(ma.sap_inbound_apply_no,'')!='' and coalesce(ma.sap_inbound_no,'')=''
group by ma.me_finish_io_no

```

```javascript
function main() {
	var success=[];
	var fail=[];
	
var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	Log.LogInfo(req_params.me_finish_io_no);

	var params={
		"method": "mes-sap-OIGN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));

	var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);

	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		ReadContext.ExecuteSql("update me_finish_io_h set sap_inbound_no='"+result.message+"',upd_time=localtimestamp where me_finish_io_no='"+finish_io_no+"'");
	}

}

return {"success":success,"fail":fail};

}

```


逻辑分析：
我来详细分析这个MES-SAP生产入库单接口的功能。
Read file: 接口_MES-SAP生产入库单.md
现在我来对这个MES-SAP生产入库单接口进行全面分析：

## MES-SAP生产入库单接口分析

### 1. 业务逻辑分析

**功能目标**: 将MES系统中已完成入库的生产订单数据推送到SAP系统，实现两系统间的数据同步。

**业务流程**:
1. MES系统完成生产入库操作（`me_finish_io_rmk4='入库完成'`）
2. 系统自动检测需要推送的入库单（有申请单号但未推送SAP）
3. 根据订单类型构建不同的JSON数据结构
4. 调用SAP接口推送数据
5. 成功后更新MES系统中的SAP入库单号

### 2. 数据逻辑分析

#### 查询条件逻辑:
```sql
where ma.me_finish_io_rmk4='入库完成' 
  and coalesce(ma.sap_inbound_apply_no,'')!='' 
  and coalesce(ma.sap_inbound_no,'')=''
```

- **状态过滤**: 只处理已完成入库的单据
- **申请单检查**: 必须有SAP入库申请单号（前置条件）
- **重复推送防护**: SAP入库单号为空，避免重复推送

#### 订单类型分类逻辑:
```sql
case when strpos(ma.mo_no,'F')=0 then
  -- 正常订单处理逻辑
else 
  -- 分割订单处理逻辑
end
```

**两种订单类型**:
1. **正常订单** (不含'F'): 基于生产订单的标准入库
2. **分割订单** (含'F'): 特殊的分割生产订单

### 3. 表关联逻辑

```mermaid
graph TD
    A[me_finish_io_h 完工入库主表] --> B[mo 生产订单表]
    A --> C[pd_part 物料主数据表]
    
    A -->|mo_no| B
    A -->|part_no| C
    
    B -->|提供会计科目和价格| D[JSON数据构建]
    C -->|提供库位信息| D
    A -->|提供入库数量和批次| D
```

**主要表关联**:
- `me_finish_io_h` ← `mo`: 通过 `mo_no` 关联，获取会计科目(`acct_code`)和价格(`price`)
- `me_finish_io_h` ← `pd_part`: 通过 `part_no` 关联，获取库位信息(`invp_area_no`)

### 4. JSON数据结构逻辑

#### 正常订单JSON结构:
```json
{
  "DocDate": "2024-01-01",
  "U_MES": "Y",
  "U_WebNo": "入库单号",
  "details": [{
    "BaseType": 202,
    "BaseRef": "生产订单号",
    "WhsCode": "库位代码",
    "QuanTity": "入库数量",
    "BatchNo": "批次号",
    "AcctCode": "会计科目",
    "U_BaseType": "FQCTEMP",
    "U_QtDocNum": "申请单号",
    "U_QtLine": 1
  }]
}
```

#### 分切订单JSON结构:
```json
{
  "DocDate": "2024-01-01",
  "U_MES": "Y", 
  "U_WebNo": "入库单号",
  "details": [{
    "ItemCode": "物料编码",
    "WhsCode": "库位代码",
    "QuanTity": "入库数量",
    "BatchNo": "批次号",
    "AcctCode": "会计科目",
    "Price": "价格",
    "U_BaseType": "SPLIT1",
    "U_OPLANNUM": "申请单号",
    "U_Reqline": "1",
    "U_QtDocNum": "原订单号(去F)",
    "U_QtLine": 1
  }]
}
```

**关键差异**:

- 正常订单: 基于`BaseRef`引用生产订单，标记为`FQCTEMP`
- 分切订单: 直接指定`ItemCode`，标记为`SPLIT1`，包含价格信息

### 5. 代码逻辑分析

#### JavaScript处理流程:
```javascript
// 1. 获取SQL查询结果
var Prev = Request.Prev;

// 2. 遍历每条入库记录
for(var i=0; i<Prev.length; i++) {
    // 3. 构建SAP接口参数
    var params = {
        "method": "mes-sap-OIGN",  // SAP库存收货接口
        "data": JSON.parse(req_params.datas)
    };
    
    // 4. 调用SAP HTTP接口
    var response = HttpApi.post(host, {body:params});
    
    // 5. 处理返回结果
    if(result.code != 0) {
        // 失败处理
        throw result.message;
    } else {
        // 成功后更新MES系统
        ReadContext.ExecuteSql(
            "update me_finish_io_h set sap_inbound_no='" + result.message + 
            "',upd_time=localtimestamp where me_finish_io_no='" + finish_io_no + "'"
        );
    }
}
```

### 6. 关键技术点

#### 数据提取技术:
- `substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1)`: 从申请单号中提取纯数字部分
- `replace(ma.mo_no,'F','')`: 分割订单去除'F'标识
- `json_agg()`: 聚合多行明细为JSON数组

#### 异常处理:
- SQL层面: 使用`coalesce()`处理空值
- JavaScript层面: 异常抛出和日志记录
- 业务层面: 状态标记防重复推送

### 7. 系统集成架构

```mermaid
sequenceDiagram
    participant MES as MES系统
    participant Interface as 接口服务
    participant SAP as SAP系统
    
    MES->>Interface: 1. 查询待推送入库单
    Interface->>Interface: 2. 构建JSON数据
    Interface->>SAP: 3. HTTP POST调用OIGN接口
    SAP-->>Interface: 4. 返回SAP入库单号
    Interface->>MES: 5. 更新sap_inbound_no
    Interface-->>MES: 6. 返回处理结果
```

### 8. 业务价值

1. **数据一致性**: 确保MES和SAP库存数据同步
2. **流程自动化**: 减少人工数据录入和错误
3. **实时性**: 入库完成即推送，保证数据时效性
4. **可追溯性**: 通过单号关联实现全链路追踪
5. **异常处理**: 完善的错误处理和重试机制

这个接口是MES-SAP集成的关键环节，实现了生产入库数据的自动化同步，是整个出入库管理体系中承上启下的重要组件。