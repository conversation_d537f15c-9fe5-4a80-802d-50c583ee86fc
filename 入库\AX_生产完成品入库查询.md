```sql
SELECT me_finish_io_no,status_name,finish_io_status,finish_io_datetime,ea_type3,mo_no,part_no,part_spec,lot_no,finish_io_qty_ok,workshop_no,me_finish_io_rmk1,workshop_worker_no,workshop_worker_name,me_finish_io_rmk4,io_times,crt_time,upd_time,upd_user_no,si_lot_h_status_name,si_conclusion_name,inspec_user_name,si_sample_qty FROM (select ass.status_name ,e.ea_type3,st.cdvl_name as si_lot_h_status_name,qm.si_conclusion_name,qm.si_sample_qty,case when coalesce(qm.si_conclusion_no,'')<>'' then qm.upd_user_name else '' end as inspec_user_name,h.* 
	from me_finish_io_h h 
	left join av_ss_status_200 ass on ass.status_no = h.finish_io_status 
	left join ea e on e.ea_no = h.workshop_no 
	left join qm_si_lot_h qm on qm.move_order_no = h.me_finish_io_no
	left join av_qm_si_status st on st.cdvl_id = qm.si_lot_h_status
order by h.finish_io_datetime desc) Tb  WHERE (crt_time >= CURRENT_DATE - INTERVAL '6 months'  
  AND crt_time < CURRENT_DATE + INTERVAL '1 day' ) 
  
```