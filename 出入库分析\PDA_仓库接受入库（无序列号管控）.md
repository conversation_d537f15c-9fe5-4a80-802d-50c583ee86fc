```sql
-- DROP FUNCTION public.af_pda_wms_prod_inbound_no_sn(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_inbound_no_sn(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（无序列号管控产品）
 * 描述：  
 * 时间：  2024/10
 * 开发者：
 * 数据：  
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_status text;
		_mo_no text;
		_part_no text;
		_part_qty text;

		_inbound_org text;
		_temp_receipt_id text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';	

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		if not exists(select 1 from ss_user where user_no=_user_no) then
			res := row('false', 'PDA关联AX用户不存在。');
			return to_json(res);
		end if;
		
		if not exists(select 1 from me_finish_io_h where me_finish_io_no=_bill_no) then
			_err_msg := format('扫描入库【%s】不存在。', _bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;		

		if not exists(select 1 from qm_si_lot_h where move_order_no=_bill_no and si_conclusion_name in ('合格','特采')) then
			res := row('false', '此入库单的完工检验单未做检验结论 或者结论不是合格。');
			return to_json(res);
		end if;

		if exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(sn_no, '')!='') then
			res := row('false', '此入库单为序列号管控产品，不能使用无序列号管控接收入库.');
			return to_json(res);
		end if;

		if not exists(select 1 from me_finish_io_h where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')='') then
			select me_finish_io_rmk4 into _bill_status from me_finish_io_h where me_finish_io_no=_bill_no;
			_err_msg := format('扫描入库【%s】单状态为【%s】，不能入库。', _bill_no,_bill_status);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		-----------------------------------------------------------------------------------------

		select (case when workshop_no='J129' then 'JXML' when workshop_no='J171' then 'JXRS' else workshop_no end) into _inbound_org 
		from me_finish_io_h where me_finish_io_no=_bill_no;

		if coalesce(_inbound_org, '')='' then
			res := row('false', '此入库单没有指定入库组织，不能入库');
			return to_json(res);
		end if;

		-----------------------------------------------------------------------------------------
		/*
		if strpos(_inbound_org,'JX')=1 then

			_temp_receipt_id := af_auid();
		
			insert into public.wm_temp_receipt
			(temp_receipt_id, temp_receipt_status, temp_receipt_no, temp_receipt_type, delivery_no, factory_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			values(_temp_receipt_id, '110', af_ss_no_generate('wm_temp_receipt_no'), '', _bill_no, 'comlink', '暂收完成', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

			insert into public.wm_temp_receipt_b
			(temp_receipt_b_id, temp_receipt_id, part_no, part_name, part_spec, part_unit, part_qty_plan, part_qty_real, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _temp_receipt_id, b.part_no, c.part_name, c.part_spec, c.part_unit, a.finish_io_qty_ok, a.finish_io_qty_ok, a.mo_no, '暂收完成', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from me_finish_io_h a
			left join mo b on b.mo_no=a.mo_no
			left join pd_part c on c.part_no=b.part_no
			where me_finish_io_no=_bill_no;
			
			update public.me_finish_io_h set me_finish_io_rmk4='暂收完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='暂收完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

		else
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		end if;
		*/
		------2025/04/23要求取消分厂回来非华为产品暂收功能---------
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;

			update public.me_finish_io set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		--------------------------------------------------------------------------------------------

		res := row('true', '--入库完成--');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);	
	END;
$function$
;

```
逻辑分析：



 我来详细分析这个`PDA_仓库接受入库（无序列号管控）.md`文件的功能。

## PDA仓库接受入库（无序列号管控）功能分析

**函数名**: `af_pda_wms_prod_inbound_no_sn`

### 1. 业务逻辑分析

**功能目标**: 专门处理那些**不通过序列号进行单件追踪**的产品的仓库入库流程。这通常适用于低价值、大批量的物料。

**核心业务流程**:
```mermaid
sequenceDiagram
    participant PDA as PDA操作员
    participant MES as MES系统
    participant QM as 质量管理模块
    
    PDA->>MES: 1. 扫描完工入库单号 (`_bill_no`)
    MES->>MES: 2. 校验用户是否存在
    MES->>MES: 3. 校验入库单是否存在
    MES->>QM: 4. 校验关联的质检单是否已判定("合格"或"特采")
    MES->>MES: 5. 校验此单是否确实为"无序列号"产品单据
    MES->>MES: 6. 校验单据状态是否为"待入库"
    MES->>MES: 7. (已废弃) 判断入库组织(本厂/分厂)
    MES->>MES: 8. **核心操作: 直接更新单据状态为"入库完成"**
    MES-->>PDA: 9. 返回成功信息
```

**关键业务规则**:
1.  **强质检依赖**: 必须先有质检结论（合格/特采），才能入库。这是一个关键的业务卡点。
2.  **严格区分管控类型**: 此功能严禁处理有序列号的产品单据，防止业务流程错乱。
3.  **流程简化**: 与有序列号的产品入库相比，此流程被大大简化。它移除了复杂的“暂收”逻辑，所有来源的产品都执行统一的、一步到位的入库操作。

### 2. 代码逻辑分析 (SQL函数)

#### a. 参数与变量
*   **输入**: `datas` (JSON字符串)，包含 `user_no` 和 `bill_no`。
*   **关键变量**: `_bill_no`, `_bill_status`, `_inbound_org`。

#### b. 前置条件校验 (Guard Clauses)
函数开头部分是一系列严格的校验，确保后续操作的合法性：
1.  **用户校验**: `if not exists(select 1 from ss_user ...)`
2.  **单据存在性校验**: `if not exists(select 1 from me_finish_io_h ...)`
3.  **质检结论校验**: `if not exists(select 1 from qm_si_lot_h ...)`
4.  **无序列号校验**: `if exists(select distinct 1 from me_finish_io ...)`
5.  **单据状态校验**: `if not exists(select 1 from me_finish_io_h ... where coalesce(me_finish_io_rmk4,'')='')`

#### c. 核心处理逻辑
**旧逻辑 (已被注释)**:
```sql
/*
if strpos(_inbound_org,'JX')=1 then
    -- 创建暂收单 (wm_temp_receipt)
    -- 更新状态为 '暂收完成'
else
    -- 更新状态为 '入库完成'
end if;
*/
```
*   **分析**: 原本存在根据入库组织 (`_inbound_org`，如江西分厂'JX')进行分流的逻辑，分厂产品需要先“暂收”。

**新逻辑 (当前生效)**:
```sql
------2025/04/23要求取消分厂回来非华为产品暂收功能---------
update public.me_finish_io_h set me_finish_io_rmk4='入库完成', ...
update public.me_finish_io set me_finish_io_rmk4='入库完成', ...
```
*   **分析**: 在`2025/04/23`进行了一次重要的业务流程简化。取消了分厂产品的暂收流程，所有无序列号产品的入库操作都被统一为**直接更新状态为“入库完成”**。

#### d. 异常处理
*   使用标准的 `EXCEPTION WHEN OTHERS` 块来捕获任何未预料的数据库错误，并以统一的JSON格式返回错误信息，保证了接口的健壮性。

### 3. 数据逻辑分析

#### 输入数据结构
```json
{
  "user_no": "PDA操作员编号",
  "datas": [{
    "bill_no": "要入库的完工单号"
  }]
}
```
*   这是一个非常精简的输入，只要求提供操作员和目标单据号。

#### 核心数据操作
*   **查询**:
    *   `ss_user`: 验证用户。
    *   `me_finish_io_h`: 获取单据头信息，如状态、车间号。
    *   `me_finish_io`: 检查是否存在序列号。
    *   `qm_si_lot_h`: 查询质检结论。
*   **更新**:
    *   `me_finish_io_h`: 将头状态 `me_finish_io_rmk4` 更新为 `'入库完成'`。
    *   `me_finish_io`: 将明细状态 `me_finish_io_rmk4` 更新为 `'入库完成'`。

*   **库存更新**: 此函数**不直接操作库存表** (`szjy_wm_inventory`)。它通过更新 `me_finish_io_h` 的状态，触发后续的后台作业或接口来增加批次库存。这是一种常见的解耦设计。

### 4. 表之间关联逻辑

```mermaid
graph TD
    subgraph "输入"
        A[PDA 输入: bill_no]
    end

    subgraph "校验与处理"
        A --> B(me_finish_io_h<br/>完工入库单头)
        B --> C{状态是否待入库?}
        B --> D(qm_si_lot_h<br/>质检单)
        B --> E(me_finish_io<br/>完工入库明细)
        
        D --> F{质检是否合格?}
        E --> G{是否无序列号?}
    end

    subgraph "结果"
        H{通过所有校验} --> I[更新 me_finish_io_h 状态]
        I --> J[更新 me_finish_io 状态]
        
        K{任一校验失败} --> L[返回错误信息]
    end

    C & F & G --> H
    C & F & G --> K

    style B fill:#lightblue
    style D fill:#lightgreen
    style E fill:#lightgreen
```

**关联描述**:
1.  **`me_finish_io_h` 与 `qm_si_lot_h`**: 通过 `move_order_no` 字段关联。一个完工入库单(`me_finish_io_h.me_finish_io_no`) 对应一个质检单(`qm_si_lot_h.move_order_no`)。这是业务流程串联的关键。
2.  **`me_finish_io_h` 与 `me_finish_io`**: 通过 `me_finish_io_no` 字段主从关联。用于检查明细中是否包含序列号。

### 总结与对比

与有序列号的入库流程相比，这个“无序列号”版本：

*   **更简单**: 逻辑路径唯一，没有复杂的暂收、复检流程。
*   **更直接**: 核心操作就是更新一个状态字段。
*   **验证更严格**: 在流程开始时增加了对“无序列号”和“质检结论”的强制校验，以防范流程误用。

这个功能的演变（从区分组织到统一处理）也反映了业务需求的调整和优化，目标是简化非核心产品的管理流程，提高操作效率。