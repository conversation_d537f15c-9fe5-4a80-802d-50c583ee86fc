```sql
-- DROP FUNCTION public.af_pda_wm_sn_up_part_no(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wm_sn_up_part_no_piliang()
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
-- PDA编码转换，转换内容 品号(part_no)、品名(part_name)、规格(part_spec) - 批量处理版本
declare
    _me_finish_io_no    text;
    _userId   text := 'system';
    _userNo text := 'system';
    _userName text := '系统';
   	result    returntype;
    _returned_sqlstate    varchar;
	_table_name			  varchar;
	_column_name          varchar;
	_pg_datatype_name     varchar;
	_constraint_name      varchar;
	_pg_exception_detail  varchar;
	_pg_exception_context varchar;
	_message_text         varchar;
    _sn_no                text;--条码编号
    _new_part_no_tp          text;--新的料号
    _new_part_no          text;--新的料号
    _new_part_name        text;--新的品名
    _new_part_spec        text;--新的规格
    _old_part_no          text;--旧的料号
    _ord_part_name        text;--旧的品名
    _ord_part_spec        text;--旧的规格
    _count int;
    _processed_count int := 0; --处理数量计数
    _error_count int := 0;     --错误数量计数
    _sn_cursor CURSOR FOR 
        select sn_no from wm_sn 
        where substring(part_no,length(substring(split_part(sn_no,'/',1),3))+2,4)='3031' 
        and sn_status='800' and sn_type_name='外箱' limit 10;

begin

    --批量处理每个符合条件的sn_no
    FOR sn_rec IN _sn_cursor LOOP
        BEGIN
            _sn_no := sn_rec.sn_no;
            
            --查询出跟换后的料号
            select replace(part_no, '3031', '3039') as new_part_no,part_no,part_name,part_spec 
            into _new_part_no_tp,_old_part_no,_ord_part_name,_ord_part_spec 
            from wm_sn where sn_no=_sn_no;

            --查询pd_part表当前料号是否存在
            select part_no,part_name,part_spec into _new_part_no,_new_part_name,_new_part_spec 
            from pd_part where part_no=_new_part_no_tp;

            --判断料号是否存在
            if coalesce(_new_part_no,'')='' then
                 _error_count := _error_count + 1;
                 --记录错误但继续处理下一条
                 RAISE NOTICE '条码: %, 转换后的料号: % 不存在!', _sn_no, _new_part_no_tp;
                 CONTINUE;
            end if;

            --更新wm_sn表的part_no,part_name,part_spec
            update wm_sn set part_no=_new_part_no,part_name=_new_part_name,part_spec=_new_part_spec,
            upd_user=_userId,upd_user_name=_userName,upd_user_no=_userNo,upd_host='PDA',upd_time=now() 
            where sn_no=_sn_no;
           
            --记录日志
            INSERT INTO public.szjy_wm_sn_up_partInfo_log(
                sn_no_box, sn_no, part_no, part_no_new,part_name,part_name_new,part_spec,part_spec_new,
                crt_time, crt_user, crt_user_no, crt_user_name, crt_host
            )
            values(_sn_no,'', _old_part_no, _new_part_no,_ord_part_name,_new_part_name,_ord_part_spec,_new_part_spec,
            NOW(), _userId, _userNo, _userName, 'PDA');

            --更新外箱的part_no,part_name,part_spec
            update wm_sn set part_no=_new_part_no,part_name=_new_part_name,part_spec=_new_part_spec,
            upd_user=_userId,upd_user_name=_userName,upd_user_no=_userNo,upd_host='PDA',upd_time=now() 
            where sn_pack_50=_sn_no;
           
            --记录外箱条码更新日志
            INSERT INTO public.szjy_wm_sn_up_partInfo_log(
                sn_no_box, sn_no, part_no, part_no_new,part_name,part_name_new,part_spec,part_spec_new,
                crt_time, crt_user, crt_user_no, crt_user_name, crt_host
            )
            SELECT _sn_no,sn_no,_old_part_no,_new_part_no,_ord_part_name,_new_part_name,_ord_part_spec,_new_part_spec,
            NOW(), _userId, _userNo, _userName, 'PDA' FROM wm_sn where sn_pack_50=_sn_no;
            
            _processed_count := _processed_count + 1;
            
        EXCEPTION
            WHEN others THEN
                _error_count := _error_count + 1;
                RAISE NOTICE '处理条码: % 时发生错误: %', _sn_no, SQLERRM;
                CONTINUE; --继续处理下一条
        END;
    END LOOP;

    result := row('true','批量处理完成! 成功处理: ' || _processed_count || ' 条, 错误: ' || _error_count || ' 条');
  	return to_json(result);
	
exception
when others then
 get STACKED diagnostics
 	    _returned_sqlstate = RETURNED_SQLSTATE,
 	    _table_name = TABLE_NAME,
		_column_name = COLUMN_NAME,
 	    _pg_datatype_name = PG_DATATYPE_NAME,
 	    _constraint_name = CONSTRAINT_NAME,
 	    _pg_exception_detail = PG_EXCEPTION_DETAIL,
 	    _pg_exception_context = PG_EXCEPTION_CONTEXT,
 	    _message_text = MESSAGE_TEXT;
       
		result := row('false',
		_message_text);
		
		return to_json(result);
		
end;

$function$
;

```