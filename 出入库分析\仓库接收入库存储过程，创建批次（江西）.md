```sql
-- DROP FUNCTION public.af_ax_wms_temp_receipt_push_qm_si_lot(varchar);

CREATE OR REPLACE FUNCTION public.af_ax_wms_temp_receipt_push_qm_si_lot(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  暂收单下推品质检验单
 * 描述：
 * 时间：
 * 开发者：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_temp_receipt_no text;
		_temp_receipt_id text;
		_qm_si_lot_no text;
		_qm_si_lot_id text;
	
		_part_qty numeric;
		
		row_datas record;
		_client_no text;
		_client_name text;
	
		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;			
		res returntype;	
	
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_host := json_datas->>'host';
		
		json_datas := json(json_datas->'selected'->0);
		_temp_receipt_no := json_datas->>'temp_receipt_no';
		_temp_receipt_id := json_datas->>'temp_receipt_id';

		--insert into a_test_log values(datas, 'push_si_lot', localtimestamp);
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		if exists(select 1 from qm_si_lot_h where move_order_no=_temp_receipt_no) then
			res := row('false', 此暂收单已经下推检验单，不能二次下推);
			return to_json(res);
		end if;
	
		----------------------------------------------------------------------------------------------------------------
		
		for row_datas in (select temp_receipt_b_id,temp_receipt_id,part_no,mo_no from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id) loop
		
			_qm_si_lot_id := af_auid();

			select client_no,client_name into _client_no,_client_name from mo where mo_no=row_datas.mo_no;
		
			insert into public.qm_si_lot_h
			(si_lot_h_id, si_lot_h_no, si_lot_h_status, factory_no, factory_name, part_no, part_name, part_spec, part_idt, wkp_no, wkp_name, si_lot_qty, si_lot_move_type, si_lot_move_type_name, move_order_h_id, move_order_b_id, move_order_id, move_order_no, order_type, order_type_name, order_h_id, order_b_id, order_id, order_no, client_no, client_name, supplier_no, supplier_name, si_type, si_type_name, si_degree, si_degree_name, si_level, si_level_name, si_aql, si_lot_qty_ok, si_lot_qty_ng, si_conclusion_no, si_conclusion_name, si_is_pass, si_lot_h_rmk01, si_lot_h_rmk02, si_lot_h_rmk03, si_lot_h_rmk04, si_lot_h_rmk05, si_lot_h_rmk06, si_lot_h_rmk07, si_lot_h_rmk08, si_lot_h_rmk09, si_lot_h_rmk10, da_switch_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, io_is_sucessed, io_times, io_last_time, delivery_order_no, si_sample_qty, si_lot_h_rmk11, si_lot_h_rmk12, si_lot_h_rmk13, si_lot_h_rmk14, si_lot_h_rmk15, si_lot_h_rmk16, si_lot_h_rmk17, si_lot_h_rmk18, si_lot_h_rmk19, si_lot_h_rmk20, si_lot_h_rmk21, si_lot_h_rmk22, si_lot_h_rmk23, si_lot_h_rmk24, si_lot_h_rmk25, si_lot_h_rmk26, si_lot_h_rmk27, si_lot_h_rmk28, si_lot_h_rmk29, si_lot_h_rmk30, si_lot_h_rmk31, price, priceafvat, lot_no, put_qty, si_lot_h_rmk32, whscode, canceled, qm_si_gist_no, ea_no, ea_name, si_lot_h_rmk33, si_lot_h_rmk34)
			select _qm_si_lot_id, af_ss_no_generate('wgjy_si_lot_h_no'), '5B8A5FD43EEB00004DFE', 'comlink', '深圳市金洋电子股份有限公司', part_no, part_name, coalesce(part_spec,''), '', '', '', part_qty_real, '315', '完工检验(复检)', _temp_receipt_id, '', _temp_receipt_id, _temp_receipt_no, 'MO', '生产订单', '', '', '', row_datas.mo_no, _client_no, _client_name, '', '', '20', '抽检', '20', '正常', '10', '一般', '', 0, 0, '', '', false, '', '', '', '', '', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '', false, 0, null, '', 0, '', '', '', '', false, '', localtimestamp, null, '', '', '', '', false, '', '', '', '', '', '', 0, '', 0, 0, '', 0, '', '', '', '', '', '', '', ''
			from wm_temp_receipt_b
			where temp_receipt_b_id=row_datas.temp_receipt_b_id;

			insert into public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name, sn_type, sn_type_name)
			select af_auid(), _qm_si_lot_id, '', a.sn_no, '', '', '', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '', '20', '', '', '', a.part_qty, b.mo_no, b.weight_gross, b.weight_net, pack_qty_used, b.ea_no, b.ea_name, b.sn_type, b.sn_type_name
			from wm_temp_receipt_sn_part a
			left join wm_sn b on b.sn_no=a.sn_no
			where temp_receipt_id=row_datas.temp_receipt_id and a.part_no=row_datas.part_no and a.mo_no=row_datas.mo_no;
	
		end loop;
	

		update public.wm_temp_receipt set temp_receipt_rmk01='下推检验单完成',push_time=localtimestamp,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where temp_receipt_no=_temp_receipt_no;
	
		res := row('true', '下推品质检验单完成');
		return to_json(res);
	
	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);			
	end;

$function$
;


```


功能逻辑：
好的，我们来深入分析 `af_ax_wms_temp_receipt_push_qm_si_lot` 这个存储过程。这个功能是连接“分厂暂收”和“正式入库”两个环节的**关键桥梁**，它的核心任务是为已完成暂收的货物创建新的质检任务。

### 功能概述

-   **功能名称**: `af_ax_wms_temp_receipt_push_qm_si_lot`
-   **中文释义**: 暂收单下推品质检验单
-   **核心用途**: 此存储过程被**自动调用**，当一个暂收单 (`wm_temp_receipt`) 收货完成时，它会读取该暂收单的所有信息，并据此在质检系统中生成一个全新的、待处理的**复检/到货检验批**。
-   **业务场景**: 这是对分厂/委外厂产品进行二次质量把关的核心步骤。产品在分厂出厂时已经过一次检验，运抵主厂后，通过此功能触发第二次检验（IQC到货检验），确保在运输过程中没有发生损坏，以及产品质量符合最终入库标准。

### 业务逻辑分析 (按执行顺序)

1.  **输入与触发**:
    *   **输入**: `temp_receipt_id` (暂收单ID) 和 `temp_receipt_no` (暂收单号)。
    *   **触发**: 由上游的 `af_pda_wms_prod_temp_receipt` 存储过程在确认暂收单已满货时自动调用。

2.  **前置条件校验 (Guard)**:
    *   `if exists(select 1 from qm_si_lot_h where move_order_no=_temp_receipt_no)`
    *   系统首先检查是否已为该暂收单创建过检验批，`move_order_no` 字段是这里的关键链接。这可以有效防止因网络重试等原因造成的重复创建检验任务。

3.  **循环创建检验批**:
    *   系统会遍历该 `_temp_receipt_id` 下的**每一条物料明细** (`wm_temp_receipt_b`)。这意味着如果一张暂收单里有多种不同的物料，系统会为每种物料创建独立的检验批。
    *   **对于每一条物料明细**:
        1.  **创建检验批头 (`qm_si_lot_h`)**:
            *   系统会向 `qm_si_lot_h` 表中插入一条新的记录。
            *   **`si_lot_move_type`**: 这个字段被设为 **'315'**，在系统中定义为 **“完工检验(复检)”**。这清晰地表明了本次检验的性质。
            *   **`move_order_no`**: 写入当前的 `_temp_receipt_no`，将检验批与暂收单关联起来。
            *   **`order_no`**: 写入原始的生产订单号 `mo_no`，保留了从生产到最终入库的完整追溯链。
            *   **`si_lot_h_status`**: 状态被设为 `'5B8A5FD43EEB00004DFE'`，这代表“待检验”状态。
        2.  **关联检验序列号 (`qm_si_lot_b_sn`)**:
            *   在创建了检验批头之后，系统会从 `wm_temp_receipt_sn_part` 表中找出属于当前物料明细的所有序列号。
            *   然后，将这些序列号批量插入到 `qm_si_lot_b_sn` 表中，并与刚刚创建的检验批头 `_qm_si_lot_id` 进行关联。
            *   这一步操作相当于告诉质检员：“对于这张检验单，你需要检验以下这些具体的序列号产品。”

4.  **更新源单状态**:
    *   在成功为所有明细行创建检验任务后，程序会回头更新 `wm_temp_receipt` (暂收单头表) 的状态。
    *   `temp_receipt_rmk01` 字段被更新为 **“下推检验单完成”**。这标志着暂收流程的结束，并将业务的接力棒正式交给了品质部门。

### 数据与表逻辑分析

-   **输入核心表 (Read)**:
    -   `wm_temp_receipt_b`: 循环的主体，提供要为哪些物料创建检验任务。
    -   `wm_temp_receipt_sn_part`: 提供需要被检验的具体序列号清单。
    -   `wm_sn`, `mo`, `ss_user`: 用于补充检验批所需的一些辅助信息，如客户、用户、产品规格等。

-   **输出/变更核心表 (Write/Update)**:
    -   `qm_si_lot_h`: **核心输出**，每循环一次就创建一条新的待检任务头记录。
    -   `qm_si_lot_b_sn`: **核心输出**，创建大量的明细记录，将实体产品（SN）与检验任务关联。
    -   `wm_temp_receipt`: **状态更新**，标志着其生命周期的结束和下游流程的开始。

-   **表间关键链接**:
    -   **读-写链接**: `wm_temp_receipt.temp_receipt_no` 的值被写入了 `qm_si_lot_h.move_order_no`，这是连接暂收和质检两个模块的“外键”。
    -   **头-明细链接**: 新生成的 `_qm_si_lot_id` 被用作 `qm_si_lot_b_sn.si_lot_h_id` 的值，建立了检验批头与明细之间的父子关系。

### 总结

`af_ax_wms_temp_receipt_push_qm_si_lot` 是一个典型的**业务流程自动化**存储过程。它扮演着“流程引擎”的角色，在暂收环节完成后，自动、准确地为品质部门创建后续工作任务，实现了业务的无缝流转。

它的存在，完美解释了 `出库分析.md` 中推断的“二次质检”环节，证实了分厂产品入库流程比本厂入库更为严格，增加了一道“到货复检”的质量关卡。