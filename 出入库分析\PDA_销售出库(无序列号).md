```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_no_sn(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_no_sn(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
*  功能：无序列号管控产品销售出库
*  描述：
*  时间：
*  开发者：
*/
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;
		_part_qty_plan numeric;
		_part_qty_stock numeric;

		row_datas record;
		stock_datas record;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;

	BEGIN
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';

		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;

		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
			res := row('false', '扫描销售发货单不存在 或者 销售发货单已经发货完成.');
			return to_json(res);
		end if;
		select cr_dlv_h_id into _bill_id from cr_dlv_h where cr_dlv_h_no=_bill_no;
		for row_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id) loop
			if not exists(select 1 from szjy_wm_inventory where part_no=row_datas.part_no and invp_area=row_datas.invp_no and part_qty>0) then
				res := row('false', format('销售出库产品 %s 发货仓库 %s 在仓库库存中不存在，不能出库', row_datas.part_no, row_datas.invp_no));
				return to_json(res);
			end if;
			
			select sum(part_qty) into _part_qty_stock from szjy_wm_inventory where part_no=row_datas.part_no;
			if row_datas.cr_dlv_qty_plan>_part_qty_stock then
				res := row('false', format('销售出库产品 %s 数量 %s 大于库存数量 %s，不能出库', row_datas.part_no, row_datas.part_qty_plan, _part_qty_stock));
				return to_json(res);
			end if;
		end loop;

		for row_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id) loop
			_part_qty_plan := row_datas.cr_dlv_qty_plan;
			for stock_datas in (select * from szjy_wm_inventory where part_no=row_datas.part_no and invp_area=row_datas.invp_no order by lot_no) loop
				if _part_qty_plan > 0 then
					if _part_qty_plan <= stock_datas.part_qty then
					
						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						values(af_auid(), row_datas.cr_dlv_h_id, '', row_datas.part_no, row_datas.part_name, row_datas.part_spec, row_datas.part_unit, '', '', '', stock_datas.lot_no, row_datas.invp_no, '', _part_qty_plan, '', '', '', '', '', row_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

						update public.szjy_wm_inventory set part_qty=part_qty-_part_qty_plan,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
						where inventory_id=	stock_datas.inventory_id;					

						_part_qty_plan := 0;
					else
						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						values(af_auid(), row_datas.cr_dlv_h_id, '', row_datas.part_no, row_datas.part_name, row_datas.part_spec, row_datas.part_unit, '', '', '', stock_datas.lot_no, row_datas.invp_no, '', stock_datas.part_qty, '', '', '', '', '', row_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');
			
						update public.szjy_wm_inventory set part_qty=0,upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
						where inventory_id=	stock_datas.inventory_id;

						_part_qty_plan := _part_qty_plan-stock_datas.part_qty;
					end if;
				end if;
			end loop;
		end loop;

		update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty_plan,cr_dlv_b_rmk6='发货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id;

		update public.cr_dlv_h set cr_dlv_h_rmk6='发货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id;

		res := row('true', '---OK---');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;


```


存储过程逻辑：

          
## PDA销售出库(无序列号)功能逻辑分析

这个功能是一个PostgreSQL存储过程，用于处理无序列号管控产品的销售出库操作。以下是详细的业务逻辑分析：

### 功能概述
- **功能名称**：`af_pda_wms_sales_outbound_no_sn`
- **主要用途**：处理不需要序列号管控的产品销售出库
- **适用场景**：批量产品、原材料等无需单独序列号跟踪的商品

### 核心业务逻辑

#### 1. 参数解析与用户验证
```sql
json_datas := json(datas);
_user_no := json_datas->>'user_no';
_bill_no := json_datas->>'bill_no';
```
- 解析输入的JSON参数，获取用户编号和发货单号
- 通过`ss_user`表验证用户身份，获取用户ID和姓名

#### 2. 发货单状态验证
```sql
if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
    res := row('false', '扫描销售发货单不存在 或者 销售发货单已经发货完成.');
end if;
```
- 检查发货单是否存在且未完成发货
- `cr_dlv_h_rmk6`字段为空表示待发货状态

#### 3. 库存充足性检查（两层验证）

**第一层：仓库库存存在性检查**
```sql
if not exists(select 1 from szjy_wm_inventory where part_no=row_datas.part_no and invp_area=row_datas.invp_no and part_qty>0) then
    -- 报错：库存不存在
end if;
```

**第二层：库存数量充足性检查**
```sql
select sum(part_qty) into _part_qty_stock from szjy_wm_inventory where part_no=row_datas.part_no;
if row_datas.cr_dlv_qty_plan>_part_qty_stock then
    -- 报错：库存数量不足
end if;
```

#### 4. 库存扣减与发货记录生成

**按批次顺序处理库存**
```sql
for stock_datas in (select * from szjy_wm_inventory where part_no=row_datas.part_no and invp_area=row_datas.invp_no order by lot_no) loop
```
- 按批次号（`lot_no`）顺序处理，实现先进先出
- 支持跨批次发货，自动分配多个批次

**两种扣减策略**：

1. **当前批次库存充足**：
   - 直接扣减所需数量
   - 生成一条发货明细记录
   - 库存数量 = 原库存 - 发货数量

2. **当前批次库存不足**：
   - 全部扣减当前批次
   - 生成发货明细记录（数量=当前批次全部库存）
   - 继续处理下一批次

#### 5. 发货明细记录生成
```sql
insert into public.cr_dlv_sn_part
(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, ...)
values(af_auid(), row_datas.cr_dlv_h_id, '', row_datas.part_no, ...);
```
- 在`cr_dlv_sn_part`表中记录发货明细
- `sn_no`字段为空（因为是无序列号产品）
- 记录批次号、仓库、数量等信息
- `cr_dlv_sn_part_rmk6`字段关联发货明细ID

#### 6. 状态更新

**更新发货明细状态**：
```sql
update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty_plan,cr_dlv_b_rmk6='发货完成'
where cr_dlv_h_id=_bill_id;
```

**更新发货单头状态**：
```sql
update public.cr_dlv_h set cr_dlv_h_rmk6='发货完成'
where cr_dlv_h_id=_bill_id;
```

### 关键特点

1. **批次管理**：按批次号顺序处理，支持先进先出原则
2. **跨批次发货**：单次发货可能涉及多个批次，自动分配
3. **原子性操作**：整个过程在事务中执行，确保数据一致性
4. **完整性检查**：多层验证确保库存充足和数据有效性
5. **审计跟踪**：记录操作用户、时间等审计信息

### 与有序列号出库的区别

- **无需扫描序列号**：直接按数量扣减库存
- **批次级别管理**：以批次为最小管理单位
- **简化流程**：无需逐个验证序列号状态
- **适用场景**：大宗商品、原材料、标准化产品等

### 异常处理
- 使用PostgreSQL的异常处理机制
- 捕获所有异常并返回错误信息
- 确保事务回滚，保持数据一致性

这个功能设计合理，既保证了库存管理的准确性，又简化了无序列号产品的出库流程，适合批量商品的快速出库操作。
        