```sql
with
t1 as (select cr_dlv_h_id,cr_dlv_h_no,client_no,cr_dlv_type from cr_dlv_h where cr_dlv_h_rmk6='发货完成' and coalesce(sap_bill_no,'')=''),
t2 as (select * from cr_dlv_b where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t3 as (select * from cr_dlv_sn_part where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t4 as (select cr_dlv_sn_part_rmk6,lot_no,invp_no,sum(part_qty) as part_qty from t3 group by cr_dlv_sn_part_rmk6,lot_no,invp_no),
t5 as (select t2.cr_dlv_h_id,t2.cr_dlv_b_id,t2.part_no,t2.price,t2.so_h_no,t2.so_b_id,coalesce(t2.cr_dlv_b_rmk5,'0') as ctn_num,
	t4.lot_no,t4.invp_no,t4.part_qty from t2 left join t4 on t4.cr_dlv_sn_part_rmk6=t2.cr_dlv_b_id ),
t6 as (select t1.cr_dlv_h_no,t1.client_no,t1.cr_dlv_type,
	t5.cr_dlv_b_id,t5.part_no,t5.price,t5.so_h_no,t5.so_b_id,t5.invp_no,sum(t5.part_qty) as part_qty_t,t5.ctn_num,json_agg(json_build_object('BatchNo',lot_no,'Qty',t5.part_qty)) as batch_no
	from t1 
	left join t5 on t5.cr_dlv_h_id=t1.cr_dlv_h_id
	group by t1.cr_dlv_h_no,t1.client_no,t1.cr_dlv_type,
	t5.cr_dlv_b_id,t5.part_no,t5.price,t5.so_h_no,t5.so_b_id,t5.invp_no,t5.ctn_num),
t7 as (select cr_dlv_h_no,cr_dlv_type,
	case when cr_dlv_type='订单' then 
	json_agg(json_build_object('BaseType','17','BaseRef',so_h_no,'BaseLine',split_part(so_b_id,'_',2),'WhsCode',invp_no,'PriceAfterVAT',price,'QuanTity',part_qty_t,'BatchNo',batch_no,
	'U_PLqty',ctn_num,'U_OPLANNUM',cr_dlv_h_no,'U_OPLNUM',split_part(cr_dlv_b_id,'_',2),'U_JCDOC',so_h_no,'U_JCLINE',split_part(so_b_id,'_',2)))
	else
	json_agg(json_build_object('ItemCode',part_no,'WhsCode',invp_no,'PriceAfterVAT',price,'QuanTity',part_qty_t,'BatchNo',batch_no,'U_PLqty',ctn_num,'U_basedoc',so_h_no,
	'U_baseline',split_part(so_b_id,'_',2),'U_OPLANNUM',cr_dlv_h_no,'U_OPLNUM',split_part(cr_dlv_b_id,'_',2)))
	end as datas
	from t6
	group by cr_dlv_h_no,cr_dlv_type)

select t7.cr_dlv_h_no,json_build_object('CardCode',t1.client_no,'DocDate',to_char(current_date,'yyyy-mm-dd'),'OwnerCode','379','U_WebNo',t1.cr_dlv_h_no,'details',t7.datas) as datas
from t1 
left join t7 on t7.cr_dlv_h_no=t1.cr_dlv_h_no

```	

```javascript
function main() {
	var success=[];
	var fail=[];

var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	//Log.LogInfo(req_params.me_finish_io_no);
	var cr_dlv_no=req_params.cr_dlv_h_no;

	var params={
		"method": "mes-sap-ODLN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));	
  var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		ReadContext.ExecuteSql("update cr_dlv_h set sap_bill_no='"+result.message+"',upd_time=localtimestamp where cr_dlv_h_no='"+cr_dlv_no+"'");
	}

}

return {"success":success,"fail":fail};
 }
```

逻辑分析：



          
## PostgreSQL中的WITH语句详解

`WITH`语句在PostgreSQL中被称为**CTE（Common Table Expressions，公用表表达式）**，是一种非常强大的SQL功能。

### 基本语法
```sql
WITH cte_name AS (
    -- 子查询
    SELECT ...
)
SELECT ... FROM cte_name;
```

### 主要特点

#### 1. **临时命名结果集**
- CTE创建一个临时的、命名的结果集
- 只在当前查询的执行期间存在
- 可以在主查询中像表一样使用

#### 2. **多个CTE定义**
```sql
WITH 
t1 AS (SELECT ...),
t2 AS (SELECT ...),
t3 AS (SELECT ... FROM t1 JOIN t2 ...)
SELECT ... FROM t3;
```

#### 3. **递归CTE**
```sql
WITH RECURSIVE cte_name AS (
    -- 基础查询（锚点）
    SELECT ...
    UNION ALL
    -- 递归查询
    SELECT ... FROM cte_name WHERE ...
)
SELECT ... FROM cte_name;
```

### 在您的SQL中的应用

在<mcfile name="sql.md" path="d:\金洋\成品出入库记录\出库\sql.md"></mcfile>文件中，WITH语句被用来：

```sql
with
t1 as (select cr_dlv_h_id,cr_dlv_h_no,client_no,cr_dlv_type from cr_dlv_h where cr_dlv_h_rmk6='发货完成' and coalesce(sap_bill_no,'')=''),
t2 as (select * from cr_dlv_b where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t3 as (select * from cr_dlv_sn_part where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
-- ... 更多CTE定义
```

### 使用优势

#### 1. **提高可读性**
- 将复杂查询分解为多个逻辑步骤
- 每个CTE都有明确的业务含义
- 代码结构清晰，易于理解和维护

#### 2. **避免重复子查询**
- 一次定义，多次使用
- 减少代码重复
- 提高查询效率

#### 3. **逐步构建复杂逻辑**
- 从简单到复杂，层层递进
- 便于调试和测试
- 易于修改和扩展

#### 4. **替代临时表**
- 无需创建物理临时表
- 自动清理，无需手动删除
- 减少数据库对象管理复杂度

### 执行机制

#### **非递归CTE**
- 每个CTE在使用时被物化（materialized）
- 结果被临时存储，可多次引用
- 执行顺序按定义顺序进行

#### **优化器处理**
- PostgreSQL优化器可能选择内联CTE
- 也可能物化CTE结果
- 取决于查询复杂度和数据量

### 实际应用场景

#### 1. **数据清洗和转换**
```sql
WITH cleaned_data AS (
    SELECT TRIM(name) as name, UPPER(status) as status
    FROM raw_table
    WHERE name IS NOT NULL
)
SELECT * FROM cleaned_data;
```

#### 2. **分层数据处理**
```sql
WITH 
base_data AS (SELECT ...),
aggregated AS (SELECT ... FROM base_data GROUP BY ...),
final_result AS (SELECT ... FROM aggregated JOIN ...)
SELECT * FROM final_result;
```

#### 3. **递归查询（如组织架构）**
```sql
WITH RECURSIVE org_tree AS (
    SELECT id, name, parent_id, 1 as level
    FROM departments WHERE parent_id IS NULL
    UNION ALL
    SELECT d.id, d.name, d.parent_id, ot.level + 1
    FROM departments d
    JOIN org_tree ot ON d.parent_id = ot.id
)
SELECT * FROM org_tree;
```

### 性能考虑

#### **优点**
- 减少重复计算
- 提高代码可维护性
- 便于查询优化

#### **注意事项**
- 大数据量时CTE可能被物化，占用内存
- 复杂的CTE链可能影响性能
- 需要根据实际情况选择是否使用CTE

### 总结

WITH语句是PostgreSQL中处理复杂查询的重要工具，特别适合：
- 需要多步骤数据处理的场景
- 复杂的业务逻辑实现
- 提高SQL代码的可读性和可维护性
- 避免创建临时表的开销

在您的销售发货单查询中，WITH语句完美地展示了如何将复杂的业务逻辑分解为清晰的步骤，从基础数据筛选到最终JSON格式化，每一步都有明确的目的和作用。
        