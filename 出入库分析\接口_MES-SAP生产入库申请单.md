```sql
select me_finish_io_no,
json_build_object('U_Type','1','U_prccode',workshop_no,'U_prcname',me_finish_io_rmk1,'U_Status','N','U_DueDate',to_char(current_date,'yyyy-mm-dd'),'U_DocDate',to_char(finish_io_datetime,'yyyy-mm-dd'),'U_Note1','','U_Crman','mes','FQCTEMP1',
	case when strpos(mo_no,'F')=1 then json_agg(json_build_object('U_BaseType','分切单','U_SpNum',REPLACE(mo_no,'F',''),'U_SpLine',1,'U_ItemCode',part_no,'U_Quantity',finish_io_qty_ok,'U_OkQTY',finish_io_qty_ok,'U_Batchnum',lot_no)) 
	else json_agg(json_build_object('U_OWdocNum',mo_no,'U_ItemCode',part_no,'U_Quantity',finish_io_qty_ok,'U_OkQTY',finish_io_qty_ok,'U_Batchnum',lot_no)) end ) as datas 
from me_finish_io_h where coalesce(me_finish_io_rmk4,'')=''  and coalesce(sap_inbound_apply_no,'')=''
group by me_finish_io_no
```

```javascript
function main(){
	var success=[];
	var fail=[];
	var Prev=Request.Prev;
	Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	var finish_io_no=req_params.me_finish_io_no;
	Log.LogInfo(req_params.me_finish_io_no);

	var params={
		"method": "mes-sap-Addon",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));

	var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
	}else{
		success.push(JSON.stringify(result));
		Log.LogInfo(result.message);
		ReadContext.ExecuteSql("update me_finish_io_h set sap_inbound_apply_no='"+result.message+"',upd_time=localtimestamp where me_finish_io_no='"+finish_io_no+"'");
	}
}

return {"success":success,"fail":fail};

}

```


逻辑分析：
我来详细分析这个MES-SAP生产入库申请单接口的功能。

## MES-SAP生产入库申请单接口分析

### 1. 业务逻辑分析

**功能目标**: 在MES系统完成生产入库后，自动向SAP系统申请创建入库单据，这是入库流程的前置步骤。

**业务流程**:
```mermaid
sequenceDiagram
    participant MES as MES生产系统
    participant Interface as 申请单接口
    participant SAP as SAP系统
    participant Inbound as 入库单接口
    
    MES->>Interface: 1. 生产完工数据
    Interface->>SAP: 2. 创建入库申请单
    SAP-->>Interface: 3. 返回申请单号
    Interface->>MES: 4. 更新申请单号
    Note over MES: 等待入库操作完成
    MES->>Inbound: 5. 推送正式入库单
```

**与入库单接口的关系**:
- **申请单接口**: 生产完工后立即执行，获取SAP申请单号
- **入库单接口**: 入库完成后执行，需要申请单号作为前置条件

### 2. 数据逻辑分析

#### 查询条件逻辑:（待确认，不合格的情况，是否需要SAP申请单号）
```sql
where coalesce(me_finish_io_rmk4,'')=''  
  and coalesce(sap_inbound_apply_no,'')=''
```

**筛选条件**:

- `me_finish_io_rmk4=''`: 入库状态为空，表示刚完工但未入库
- `sap_inbound_apply_no=''`: 申请单号为空，避免重复申请

**业务状态流转**:

```
生产完工 → 申请单接口 → 入库操作 → 入库单接口
(rmk4='') → (有申请单号) → (rmk4='入库完成') → (有入库单号)
```

#### 订单类型判断逻辑:
```sql
case when strpos(mo_no,'F')=1 then
  -- 分切单处理 (订单号以'F'开头)
  json_agg(json_build_object(
    'U_BaseType','分切单',
    'U_SpNum',REPLACE(mo_no,'F',''),  -- 去除F前缀
    'U_SpLine',1,
    'U_ItemCode',part_no,
    'U_Quantity',finish_io_qty_ok,
    'U_OkQTY',finish_io_qty_ok,
    'U_Batchnum',lot_no
  ))
else 
  -- 正常生产单处理
  json_agg(json_build_object(
    'U_OWdocNum',mo_no,  -- 直接使用生产订单号
    'U_ItemCode',part_no,
    'U_Quantity',finish_io_qty_ok,
    'U_OkQTY',finish_io_qty_ok,
    'U_Batchnum',lot_no
  ))
end
```

### 3. 表关联逻辑

```mermaid
graph TD
    A[me_finish_io_h 完工入库主表] --> B[JSON数据构建]
    
    A -->|直接提取字段| C[workshop_no 车间号]
    A -->|直接提取字段| D[me_finish_io_rmk1 备注1]
    A -->|直接提取字段| E[finish_io_datetime 完工时间]
    A -->|直接提取字段| F[mo_no 生产订单号]
    A -->|直接提取字段| G[part_no 物料编码]
    A -->|直接提取字段| H[finish_io_qty_ok 合格数量]
    A -->|直接提取字段| I[lot_no 批次号]
    
    B --> J[SAP申请单JSON]
```

**数据来源**:
- 仅依赖 `me_finish_io_h` 一张表
- 无需关联其他表，数据完整性依赖该表本身
- 通过 `group by me_finish_io_no` 确保每个完工单独立处理

### 4. JSON数据结构逻辑

#### 申请单头信息:
```json
{
  "U_Type": "1",                    // 固定类型
  "U_prccode": "workshop_no",       // 车间代码
  "U_prcname": "me_finish_io_rmk1", // 车间名称/备注
  "U_Status": "N",                  // 初始状态为N(新建)
  "U_DueDate": "2024-01-01",        // 当前日期
  "U_DocDate": "2024-01-01",        // 完工日期
  "U_Note1": "",                    // 备注为空
  "U_Crman": "mes",                 // 创建人固定为mes
  "FQCTEMP1": [...]                 // 明细数组
}
```

#### 分切单明细结构:
```json
"FQCTEMP1": [{
  "U_BaseType": "分切单",
  "U_SpNum": "原订单号",      // 去除F前缀
  "U_SpLine": 1,
  "U_ItemCode": "物料编码",
  "U_Quantity": "完工数量",
  "U_OkQTY": "合格数量",
  "U_Batchnum": "批次号"
}]
```

#### 正常单明细结构:
```json
"FQCTEMP1": [{
  "U_OWdocNum": "生产订单号",   // 直接使用原订单号
  "U_ItemCode": "物料编码",
  "U_Quantity": "完工数量",
  "U_OkQTY": "合格数量", 
  "U_Batchnum": "批次号"
}]
```

### 5. 代码逻辑分析

#### JavaScript处理流程:
```javascript
// 1. 获取待处理的完工数据
var Prev = Request.Prev;

// 2. 逐条处理每个完工单
for(var i=0; i<Prev.length; i++) {
    // 3. 构建SAP Addon接口参数
    var params = {
        "method": "mes-sap-Addon",  // SAP自定义表接口
        "data": JSON.parse(req_params.datas)
    };
    
    // 4. 调用SAP HTTP接口
    var response = HttpApi.post(host, {body:params});
    
    // 5. 处理返回结果
    if(result.code != 0) {
        // 失败记录但不中断
        fail.push(result.message);
    } else {
        // 成功后更新申请单号
        ReadContext.ExecuteSql(
            "update me_finish_io_h set sap_inbound_apply_no='" + result.message + 
            "',upd_time=localtimestamp where me_finish_io_no='" + finish_io_no + "'"
        );
    }
}
```

**关键差异对比**:
| 特性 | 申请单接口 | 入库单接口 |
|-----|----------|----------|
| **调用时机** | 生产完工后 | 入库完成后 |
| **SAP方法** | `mes-sap-Addon` | `mes-sap-OIGN` |
| **数据来源** | 仅`me_finish_io_h` | 关联`mo`、`pd_part` |
| **错误处理** | 记录但继续 | 抛出异常中断 |
| **更新字段** | `sap_inbound_apply_no` | `sap_inbound_no` |

### 6. 业务场景分析

#### 分切单业务场景:
```mermaid
graph TD
    A[原生产订单: 12345] --> B[分切操作]
    B --> C[分切单: F12345]
    C --> D[申请单接口]
    D --> E[SAP申请单: U_SpNum=12345]
    E --> F[后续入库处理]
```

**分切单特点**:
- 订单号以'F'开头标识
- SAP中记录原订单号(去F)和分切标识
- 用于大批量订单的分批处理

#### 正常单业务场景:
```mermaid
graph TD
    A[生产订单: 12345] --> B[正常完工]
    B --> C[申请单接口]
    C --> D[SAP申请单: U_OWdocNum=12345]
    D --> E[后续入库处理]
```

### 7. 系统集成时序

```mermaid
sequenceDiagram
    participant Production as 生产系统
    participant MES as MES完工
    participant ApplyInterface as 申请单接口
    participant SAP as SAP系统
    participant InboundInterface as 入库单接口
    
    Production->>MES: 1. 生产完工
    MES->>ApplyInterface: 2. 触发申请单创建
    ApplyInterface->>SAP: 3. 创建入库申请单
    SAP-->>ApplyInterface: 4. 返回申请单号
    ApplyInterface->>MES: 5. 更新申请单号
    
    Note over MES: 等待仓库入库操作
    
    MES->>MES: 6. 执行入库操作
    MES->>InboundInterface: 7. 触发正式入库
    InboundInterface->>SAP: 8. 创建正式入库单
    SAP-->>InboundInterface: 9. 返回入库单号
    InboundInterface->>MES: 10. 更新入库单号
```

### 8. 关键技术特点

#### 数据处理技术:
- **字符串处理**: `strpos(mo_no,'F')=1` 精确判断F开头
- **字符串替换**: `REPLACE(mo_no,'F','')` 去除F前缀
- **JSON聚合**: `json_agg()` 处理多明细行
- **日期格式化**: `to_char(date,'yyyy-mm-dd')` 标准化日期

#### 异常处理策略:
- **容错设计**: 单条失败不影响其他记录处理
- **状态防护**: 双重条件避免重复申请
- **日志记录**: 完整的处理过程日志

### 9. 业务价值

1. **流程自动化**: 完工即申请，无需人工干预
2. **数据准确性**: 直接从完工数据生成申请，避免录入错误
3. **状态管控**: 通过申请单号实现流程控制
4. **分类处理**: 支持正常单和分切单的差异化处理
5. **系统解耦**: 通过申请单实现MES和SAP的松耦合

这个接口是整个生产入库流程的起点，为后续的正式入库操作提供了必要的SAP申请单号，是MES-SAP集成架构中的重要组件。