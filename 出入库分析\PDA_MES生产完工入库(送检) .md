
```SQL
-- DROP FUNCTION public.af_me_finish_io_h_scan(varchar);

CREATE OR REPLACE FUNCTION public.af_me_finish_io_h_scan(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
 ---分三步　      　第一　插入　条码表　
 --         第二　 插入　出库表身 　 
 --         第三     插入　出库表头  
declare
    _datas json;
    results returntype;
   _user_no varchar;
   _scan json;
	_cr_dlv_h_id  varchar ;  -- 发货单 
    _cr_dlv_h_no  varchar; -- 通知单
   _time timestamp;  
    _today date;  
   _xsck  varchar; 
	  rb record; 
	 _tday text;   
	_ea_no text;
	_mo_no text;
   _user_id text;
   _user_name text;
  _me_finish_io_id_h text;
  _ea_name text;
  _me_finish_io_no text;
  _si_lot_h_id	text;
  _si_lot_h_no text;
 
_lot_no text;

_prod_cycle_cnt int;

	_part_qty numeric;
 	row_datas record;	

begin
	_time:=now();  
	_cr_dlv_h_id:=af_auid();   
	_tday:=to_char(current_date,'yyyy-mm-dd');  	    
             
	_datas := json(datas);
	_scan:=_datas->'datas';
	_user_no:=_datas->>'user_no';
	 -- _mo_no:=_scan->0->>'mo_no';

	raise notice '_time  %, _id  %, _user_no  % ,  _cr_dlv_h_no  %',
                 _time,_cr_dlv_h_id,_user_no,_cr_dlv_h_no  ; 
      
     select su.user_id ,su.user_name into _user_id,_user_name from ss_user su where su.user_no =_user_no; 

	create temp table temp_table_scwgsj as (select a.ea_no,a.sn_no,b.mo_no,b.part_no,b.part_qty  from json_to_recordset(json(_scan))as a(sn_no text, ea_no text)
											left join wm_sn b on b.sn_no=a.sn_no);

	----------2025/04/07 增加不同周期产品分开提交的管控(仅支持华为产品)-------------------------------------------
	select count(prod_cycle) into _prod_cycle_cnt
	from (select distinct substring(split_part(sn_no,'/021748',2),1,4) as prod_cycle from temp_table_scwgsj) tt;
	if _prod_cycle_cnt>1  then
		results := row('false', format('提交生产入库的产品存在 %s 个周期，请分开提交.', _prod_cycle_cnt));
		return to_json(results);
	end if;
	----------------------------------------------------

	for row_datas in (select distinct ea_no, mo_no, part_no from temp_table_scwgsj) loop

		select sum(part_qty) into _part_qty from temp_table_scwgsj where mo_no=row_datas.mo_no;
		select e.ea_name into _ea_name  from ea e where e.ea_no = row_datas.ea_no ;  

    	_me_finish_io_id_h:=af_auid();
    	_si_lot_h_id:=af_auid();
    	_me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    	_si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);

		_lot_no := concat('M',to_char(current_date,'YYMMDD'),row_datas.mo_no);
		--写入库单表头    
    	insert into public.me_finish_io_h
			(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
			mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
			invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
			invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
			me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
			fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, mo_type)
		values ( 
			_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
			row_datas.mo_no, row_datas.part_no, '', '', '', '', _lot_no, _part_qty, 
			'', 0, '', 0, '', 0, 
			'', row_datas.ea_no, _user_no, _user_name, 
			_ea_name, '20', '', '', 
			'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
			false, 0, null, '');

     	---写入表明细
     	insert into public.me_finish_io
			(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
			part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
			finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
			crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, sn_no)		
		select af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,pp.part_spec ,
			pp.part_unit ,'',_lot_no,ws.part_qty ,'',0,'',0,'',
			0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
		from temp_table_scwgsj a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit
		where a.mo_no=row_datas.mo_no;
	

 		insert into qm_si_lot_h(
			si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
			part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
			move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
			order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
			si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
			si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
			si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
			si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
			da_switch_id,ea_no,ea_name,
			crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
			upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
			io_is_sucessed,io_times,io_last_time)
		select 
			_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE',m.factory_no,sf.factory_name,m.part_no,p.part_name,
			p.part_spec,null,'','',_part_qty,'310','完工检验',null,null,
			_me_finish_io_id_h,_me_finish_io_no,'MO','生产订单',_me_finish_io_id_h,null,null,m.mo_no ,m.client_no,
			m.client_name,null,null,'20','抽检','20','正常','10','一般','',0,0,
			null,null,false,null,null,
			null,null,null,null,null,null,
			null,null,null,null,null,null,
			null,row_datas.ea_no,_ea_name,
			now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			false,0,null
		from (select * from mo where mo_no=row_datas.mo_no) m
		left join ss_factory sf on sf.factory_no = m.factory_no
		left join pd_part p on p.part_no=m.part_no;


		--写入送检单条码明细
		INSERT INTO public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
			si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
			si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
			mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
			sn_type,sn_type_name)
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		left join wm_sn ws on a.sn_no = ws.sn_no
		where a.mo_no=row_datas.mo_no 	
		union all 
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		join wm_sn ws on a.sn_no = ws.sn_pack_50
		where a.mo_no=row_datas.mo_no;

	end loop;

/*   -----2024/09/03 disable----------------------
	  _ea_no:=_scan->0->>'ea_no';
	 
	 
	raise notice '_time  %, _id  %, _user_no  % ,  _cr_dlv_h_no  %',
                 _time,_cr_dlv_h_id,_user_no,_cr_dlv_h_no  ; 
      
     select su.user_id ,su.user_name into _user_id,_user_name from ss_user su where su.user_no =_user_no;  
     select e.ea_name into _ea_name  from ea e where e.ea_no = _ea_no ;   
    _me_finish_io_id_h:=af_auid();
    _si_lot_h_id:=af_auid();
    _me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    _si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);
   
    if exists (select  1             
			 from json_to_recordset(json(_scan))as a(sn_no varchar)) then 
  
   --写入库单表头    
    INSERT INTO public.me_finish_io_h
		(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
		mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
		invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
		invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
		me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
		fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
		upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
		io_is_sucessed, io_times, io_last_time, mo_type)
	values ( 
	_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
	'', '', '', '', '', '', '', 0, 
	'', 0, '', 0, '', 0, 
	'', _ea_no, _user_no, _user_name, 
	_ea_name, '20', '', '', 
	'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
   false, 0, null, '');

    create temp table temp_table_scwgsj as (select  distinct  a.sn_no  from json_to_recordset(json(_scan))as a(sn_no varchar));	
     ---写入表明细
     INSERT INTO public.me_finish_io
		(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
		part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
		finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
		crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
		io_is_sucessed, io_times, io_last_time, sn_no)
		
		select 
		af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,ws.part_spec ,
		pp.part_unit ,'','',ws.part_qty ,'',0,'',0,'',
		0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
	from temp_table_scwgsj a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit 
		;
	
	---生成检验单
 	insert into qm_si_lot_h(
		si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
		part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
		move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
		order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
		si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
		si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
		si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
		si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
		da_switch_id,ea_no,ea_name,
		crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
		upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
		io_is_sucessed,io_times,io_last_time
	)
	select 
		_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE'as si_lot_h_status,m.factory_no,sf.factory_name,ws.part_no,ws.part_name,
		ws.part_spec,null,ws.wkp_no ,ws.wkp_name ,coalesce(sum(ws.part_qty),0) si_lot_qty,'310'as si_lot_move_type,'完工检验'as si_lot_move_type_name,null as move_order_h_id,null as move_order_b_id,
		_me_finish_io_id_h as move_order_id,_me_finish_io_no,'MO'as order_type,'生产订单'as order_type_name,_me_finish_io_id_h,null as order_b_id,null as order_id,m.order_no1 ,m.client_no,
		m.client_name,null as supplier_no,null as supplier_name,'20'as si_type,'抽检'as si_type_name,'20'as si_degree,'正常'as si_degree_name,'10'as si_level,'一般'as si_level_name,''as si_aql,0,0,
		null,null,false,null,null,
		null,null,null,null,null,null,
		null,null,null,null,null,null,
		null,_ea_no,_ea_name,
		now(),_user_id,_user_no,_user_name,'pda',
		now(),_user_id,_user_no,_user_name,'pda',
		false,0,null
	from temp_table_scwgsj a
	left join wm_sn ws on ws.sn_no = a.sn_no
	left join mo m on ws.mo_no = m.mo_no 
	left join ss_factory sf on sf.factory_no = m.factory_no 
	group by _si_lot_h_id,_si_lot_h_no,m.factory_no,sf.factory_name,ws.part_no,ws.part_name,
		ws.part_spec,ws.wkp_no ,ws.wkp_name ,
		_me_finish_io_no,_me_finish_io_id_h,m.order_no1 ,m.client_no,
		m.client_name ;
	
	--写入送检单条码明细
	INSERT INTO public.qm_si_lot_b_sn
	(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
	si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
	si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
	upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
	sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
	mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
	sn_type,sn_type_name)
	select 
	af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
	null,null,null,null,null,
	null,now(),_user_id,_user_no,_user_name,'pda',
	now(),_user_id,_user_no,_user_name,'pda',
	ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
	ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
	ws.sn_type,ws.sn_type_name
	from temp_table_scwgsj a
	left join wm_sn ws on a.sn_no = ws.sn_no 	
	union all 
	select 
	af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
	null,null,null,null,null,
	null,now(),_user_id,_user_no,_user_name,'pda',
	now(),_user_id,_user_no,_user_name,'pda',
	ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
	ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
	ws.sn_type,ws.sn_type_name
	from temp_table_scwgsj a
	left join wm_sn ws on a.sn_no = ws.sn_pack_50  	
	;
	
	
 end if;   

*/
 	
	  
	results := row('true','成功!');      
    return to_json(results);   
end;  
 $function$
;

```

逻辑分析：
好的，我来对 `PDA_MES生产完工入库(送检) .md` 文件中唯一的存储过程 `af_me_finish_io_h_scan` 进行一次全面的分析。

这个函数正是我们之前推断的、在用户点击“提交”后负责**批量生成单据**的核心后台程序。

---

### 功能概述

-   **功能名称**: `af_me_finish_io_h_scan`
-   **中文释义**: 扫描生成生产完工入库单
-   **核心用途**: 此函数是“生产完工送检”流程的**最终执行者**。它接收 PDA 前端汇总的、已经过初步校验的条码列表，然后**批量地、原子性地**创建“生产完工入库单”和与之配套的“品质检验批”，正式启动后续的质检流程。
-   **业务场景**: 在车间现场，当操作员使用PDA完成对一批产品的外箱扫描后，点击“提交”按钮，前端将收集到的所有条码信息一次性发送给此函数进行处理。

### 业务逻辑分析 (按执行顺序)

1.  **数据接收与预处理**:
    *   接收包含 `user_no` 和一个 `datas` 数组（内含 `sn_no` 和 `ea_no`）的JSON字符串。
    *   **关键优化**: 它首先将传入的所有条码信息加载到一个**临时表 (`temp_table_scwgsj`)** 中。这是一个非常高效的做法，避免了在循环中反复操作JSON，为后续的批量处理和分组奠定了性能基础。

2.  **整批业务规则校验**:
    *   **生产周期管控**:
        ```sql
        select count(distinct substring(split_part(sn_no,'/021748',2),1,4)) from temp_table_scwgsj
        ```
        *   这是一个针对特定产品（如华为）的特殊业务规则。它会解析所有提交的条码，提取出代表“生产周期”的字段。
        *   如果整批产品包含了**多个不同**的生产周期，操作将被驳回，强制要求用户必须将同一周期的产品一起提交。这是一个非常细致的质量或批次追溯管控点。

3.  **分组处理与单据生成 (核心)**:
    *   此函数最核心的逻辑是**分组生成**。它并不为所有产品生成一张大而全的单据，而是通过 `LOOP` 遍历 `distinct ea_no, mo_no, part_no` 的方式，为**每一个不同的“产线-工单-物料”组合，分别创建一套独立的单据**。
    *   **在每一次循环中（即每一个分组）**:
        1.  **生成批号**: 自动创建一个格式为 `'M' + 'YYMMDD' + '工单号'` 的唯一批次号 (`_lot_no`)。
        2.  **创建完工入库单**:
            *   **头表 (`me_finish_io_h`)**: 插入一条记录，状态为 `'200'` (初始态)，并记录该分组的总数量。
            *   **明细表 (`me_finish_io`)**: 为该分组下的**每一个外箱条码**都插入一条明细记录。
        3.  **创建品质检验批**:
            *   **头表 (`qm_si_lot_h`)**: 插入一条记录，检验类型 `si_lot_move_type` 设为 **'310' (完工检验)**。
            *   **关键关联**: `move_order_no` 字段被赋值为刚刚创建的完工入库单号。**这是打通“生产”与“质检”两个模块数据的核心纽带**。
            *   **明细表 (`qm_si_lot_b_sn`)**:
                *   使用 `UNION ALL` 将该分组的**外箱条码**以及这些外箱所包含的**所有内盒/产品条码**（通过 `sn_pack_50` 字段关联）**一并**插入。这确保了质检时拥有最完整的追溯链，可以追查到每一个最小包装单位。

### 代码逻辑分析

-   **临时表 (`temp_table_scwgsj`)**: 函数的性能基石。通过 `json_to_recordset` 将JSON数据关系化，便于后续使用标准SQL进行查询和分组。
-   **循环 (`FOR LOOP`)**: 核心的控制结构，确保了不同业务实体（不同工单、物料）的单据被清晰地分开创建，避免了数据混淆。
-   **ID与单号生成**: 大量使用 `af_auid()` (生成唯一ID) 和 `af_ss_no_generate()` (生成业务单号)，保证了数据的主键唯一性和业务单据的连续性。
-   **`UNION ALL` 插入**: 在插入检验批明细时，巧妙地使用了 `UNION ALL`，一次性将外箱和内件两种不同关联关系的数据全部查询出来并插入，代码简洁高效。

### 数据逻辑与表关联分析

-   **输入核心数据**:
    -   `JSON`: 包含 `sn_no` 和 `ea_no` 的列表。

-   **写入/输出核心表**:
    1.  `me_finish_io_h` (完工入库单-头)
    2.  `me_finish_io` (完工入库单-明细)
    3.  `qm_si_lot_h` (品质检验批-头)
    4.  `qm_si_lot_b_sn` (品质检验批-序列号明细)

-   **表间关键关联逻辑**:
    ```mermaid
    graph TD
        subgraph "输入 (临时表)"
            A[temp_table_scwgsj <br/> (sn_no, mo_no, part_no...)]
        end

        subgraph "输出 (数据库)"
            B[me_finish_io_h]
            C[me_finish_io]
            D[qm_si_lot_h]
            E[qm_si_lot_b_sn]
        end

        A -- 为每个分组创建 --> B
        A -- 为每个SN创建 --> C
        
        B -- 1..1 --> D
        B -- "move_order_no" --> D

        C -- 1..* --> B
        C -- "me_finish_io_no" --> B

        D -- 1..* --> E
        D -- "si_lot_h_id" --> E

        style B fill:#f9f,stroke:#333,stroke-width:2px
        style C fill:#f9f,stroke:#333,stroke-width:2px
        style D fill:#f9f,stroke:#333,stroke-width:2px
        style E fill:#f9f,stroke:#333,stroke-width:2px
    ```
    -   **核心链接**: `me_finish_io_h.me_finish_io_no` 的值被写入 `qm_si_lot_h.move_order_no`，从而将“完工单”和“检验批”这两个原本独立的业务对象紧密地关联在了一起。

### 总结

`af_me_finish_io_h_scan` 是一个设计精良的后端批量处理程序。它与之前的校验函数 `af_jiaoyantiaoma_finish_io` 完美配合，构成了“完工送检”功能的完整闭环。

它的设计亮点在于：
-   **高效**: 使用临时表处理批量数据。
-   **严谨**: 通过分组循环，确保了不同业务实体的单据隔离，数据清晰。
-   **完整**: 在创建单据的同时，原子性地创建了下游的质检任务，保证了业务流程的无缝衔接和数据的完整性。