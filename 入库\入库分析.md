​            


filePath: d:\金洋\成品出入库记录\入库接口\接口_MES-SAP生产入库单.md
d:\金洋\成品出入库记录\入库接口\接口_MES-SAP生产入库申请单.md
d:\金洋\成品出入库记录\入库接口\PDA_仓库接受入库（无序列号管控）.md
          

# 成品出入库记录入库接口使用的数据库表分析

根据分析这些接口文件，以下是使用到的数据库表及其用途：

## 数据库表清单

| 序号 | 表名 | 中文名称 | 主要用途 | 使用接口 |
|------|------|----------|----------|----------|
| 1 | me_finish_io_h | 生产完工入库主表 | 存储生产完工入库单头信息 | 所有接口 |
| 2 | me_finish_io | 生产完工入库明细表 | 存储生产完工入库单明细信息 | PDA_MES生产完工入库 |
| 3 | mo | 生产订单表 | 存储生产订单信息 | 接口_MES-SAP生产入库单 |
| 4 | pd_part | 物料主数据表 | 存储物料基础信息 | 接口_MES-SAP生产入库单 |
| 5 | wm_sn | 序列号管理表 | 存储产品序列号信息 | PDA_MES生产完工入库 |
| 6 | qm_si_lot_h | 检验批次主表 | 存储质量检验批次信息 | AX_生产完工检验, PDA_MES生产完工入库 |
| 7 | qm_si_lot_b_sn | 检验批次序列号表 | 存储检验批次对应的序列号 | PDA_MES生产完工入库 |
| 8 | ss_cdvl | 系统代码值表 | 存储系统代码值信息 | AX_生产完工检验 |
| 9 | ea | 设备/产线表 | 存储设备和产线信息 | PDA_MES生产完工入库, AX_生产完成品入库查询 |
| 10 | mo_rmb | 生产订单资源表 | 存储生产订单资源信息 | PDA_MES生产完工入库 |
| 11 | mo_rmb_ea | 生产订单资源设备关联表 | 关联生产订单资源和设备 | PDA_MES生产完工入库 |
| 12 | av_ss_status_200 | 状态视图表 | 状态信息视图 | AX_生产完成品入库查询 |
| 13 | av_qm_si_status | 检验状态视图表 | 检验状态信息视图 | AX_生产完成品入库查询 |
| 14 | av_qm_si_gist | 检验要点视图表 | 检验要点信息视图 | AX_生产完工检验 |
| 15 | qm_si_status_sy_type | 检验状态系统类型表 | 检验状态系统类型信息 | AX_生产完工检验 |
| 16 | szjy_oqc_gc_disqualified | OQC过程不合格表 | 存储OQC过程不合格信息 | PDA_MES生产完工入库 |
| 17 | ss_io_tbl.io_write_auth | 接口写入权限表 | 存储接口写入权限配置 | 接口调用 |
| 18 | a_test_log | 测试日志表 | 存储测试日志信息 | 调试用途 |

## 表关系说明

### 核心业务流程表关系
1. **me_finish_io_h** ← **me_finish_io** (一对多关系，主表与明细表)
2. **me_finish_io_h** → **mo** (通过mo_no关联生产订单)
3. **me_finish_io_h** → **pd_part** (通过part_no关联物料信息)
4. **wm_sn** → **mo** (序列号关联生产订单)
5. **qm_si_lot_h** → **me_finish_io_h** (检验批次关联入库单)

### 检验相关表关系
1. **qm_si_lot_h** ← **qm_si_lot_b_sn** (检验批次与序列号关联)
2. **qm_si_lot_h** → **av_qm_si_gist** (检验批次关联检验要点)
3. **qm_si_lot_h** → **szjy_oqc_gc_disqualified** (检验批次关联不合格记录)

### 设备产线关系
1. **mo** → **mo_rmb** → **mo_rmb_ea** → **ea** (生产订单关联设备产线)

这些表构成了完整的生产完工入库业务流程，涵盖了从生产订单、物料管理、序列号跟踪、质量检验到最终入库的全过程数据管理。
        