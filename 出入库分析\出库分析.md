### 一、 整体业务流程概述

整个成品出入库流程是一个从生产、质检、入库到仓储、销售、拣货、出库的完整闭环。

1.  **入库流程**:
    *   生产线下线的产品（外箱）通过 `PDA完工送检` 进入待检状态。
    *   质检部门进行 `生产完工检验`，给出"合格"、"特采"或"不合格"结论。
    *   检验合格后，仓库通过 `PDA仓库接收入库`（区分有无序列号）将成品扫码入库，此时库存增加，产品状态更新为"在库"。
    *   最后，系统通过接口将 `生产入库申请单` 和 `生产入库单` 推送给SAP，完成财务记账。

2.  **出库流程**:
    *   销售订单（来自SAP）在MES系统中生成 `销售发货单`。
    *   仓库根据发货单进行 `PDA销售出库拣货`（有序列号）或 `PDA销售出库`（无序列号）。
    *   拣货/出库过程中，系统会进行严格的库存、批次和（或）序列号校验（如FIFO先进先出）。
    *   拣货完成后，对应库存被锁定或扣减，产品状态更新为"拣货"。
    *   所有产品拣货完成后，发货单状态更新为"拣货完成"或"发货完成"。
    *   最后，系统通过接口将 `销售发货单` 推送给SAP，完成财务记账和发运流程。

### 二、 核心数据库表及关联分析

以下是整个出入库流程中涉及的核心数据库表及其用途。

| 类别 | 表名 | 中文名称 | 主要用途 |
| :--- | :--- | :--- | :--- |
| **主数据** | `pd_part` | 物料主数据表 | 存储物料基础信息，如是否FIFO管控。 |
| | `ea` | 设备/产线表 | 存储产线信息，用于生产和入库校验。 |
| | `ss_user` | 用户表 | 记录所有PDA及系统操作员信息。 |
| | `client` | 客户主数据 | 存储客户信息，关联销售发货单。 |
| **生产** | `mo` | 生产订单表 | 存储生产订单信息，是产品追溯的源头。 |
| **仓储/库存** | `wm_sn` | **序列号管理表** | **核心表**，跟踪每个序列号产品的生命周期状态（已确认/在库/拣货/出库）。 |
| | `szjy_wm_inventory` | **库存表** | **核心表**，存储无序列号产品的库存信息（物料、批次、仓库、数量）。 |
| **入库流程** | `wm_temp_receipt` | **暂收单主表** | **核心表(分厂)**，管理分厂/委外到货的暂收操作。 |
| | `wm_temp_receipt_b` | 暂收单明细表 | 按物料汇总暂收的计划与实收数量。 |
| | `wm_temp_receipt_sn_part` | 暂收序列号表 | 记录暂收的每一个产品序列号。 |
| | `me_finish_io_h` | 生产完工入库主表 | 记录完工入库单的头信息和状态。 |
| | `me_finish_io` | 生产完工入库明细表 | 记录完工入库单扫描的序列号等明细。 |
| | `qm_si_lot_h` | 检验批次主表 | 存储质检信息和检验结论。 |
| **出库流程** | `` | 销售订单头表 | (逻辑存在) 在 `cr_dlv_b` 中通过 `so_h_no` 引用, 是销售流程的源头。 |
| | `cr_dlv_h` | **销售发货单主表** | **核心表**，记录销售发货的头信息和状态（待拣货/拣货完成/发货完成）。 |
| | `cr_dlv_b` | 销售发货单明细表 | 记录发货单需要发运的物料和数量。 |
| | `cr_dlv_sn_part` | **发货序列号/批次表** | **核心表**，记录为发货单拣货/出库的每个序列号或批次数量。 |
| **退货流程** | `cr_rtn_h` | 销售退货单主表 | 记录销售退货的头信息。 |

### 三、 数据流与表关系图

下图清晰地展示了出入库业务中核心表之间的数据流动和关联关系。

```mermaid
graph TD
    subgraph "生产与入库流程"
        MO[("mo<br/>生产订单")] -->|生成| WM_SN_In("wm_sn<br/>序列号 110-确认")
        WM_SN_In -->|完工送检| ME_FINISH_IO_H("me_finish_io_h<br/>完工入库单")
        ME_FINISH_IO_H -->|关联| ME_FINISH_IO("me_finish_io<br/>完工入库明细")
        ME_FINISH_IO_H -->|创建| QM_SI_LOT_H("qm_si_lot_h<br/>出厂检验批")
        
        QM_SI_LOT_H -->|检验合格| ME_FINISH_IO_H
        
        subgraph "仓库接收 (按来源分流)"
            direction TB
            ME_FINISH_IO_H -->|本厂产品| Proc_Local_Inbound{"af_pda_wms_prod_factory_inbound<br/>本厂直接入库"}
            ME_FINISH_IO_H -->|分厂/江西产品| Proc_Remote_Temp_Inbound{"af_pda_wms_prod_temp_receipt<br/>分厂暂收"}
        end
        
        Proc_Local_Inbound --> WM_SN_Update("wm_sn<br/>序列号 800-在库")

        Proc_Remote_Temp_Inbound --> WM_TEMP_H("wm_temp_receipt<br/>暂收单")
        Proc_Remote_Temp_Inbound --> WM_SN_Temp("wm_sn<br/>序列号 840-暂收")
        WM_TEMP_H -->|触发| QM_SI_LOT_H2("qm_si_lot_h<br/>到货检验批")
        QM_SI_LOT_H2 -->|检验合格| Final_Inbound{"正式入库操作<br/>(暂收转入库)"}
        Final_Inbound --> WM_SN_Update
        
        WM_SN_Update -->|非序列号产品| SZJY_WM_INVENTORY_IN("szjy_wm_inventory<br/>库存增加")

        subgraph "SAP接口"
            direction LR
            ME_FINISH_IO_H -- "状态:入库完成" --> SAP_In_Doc["接口: 生产入库单"]
        end
    end

    subgraph "仓储核心"
        direction TB
        WM_SN_Storage("wm_sn<br/>序列号-在库")
        SZJY_WM_INVENTORY("szjy_wm_inventory<br/>批次库存")
    end

    subgraph "销售与出库流程"
        SO[("销售订单<br/>(业务源头)")] -->|创建| CR_DLV_H("cr_dlv_h<br/>销售发货单")
        CR_DLV_H -->|关联| CR_DLV_B("cr_dlv_b<br/>发货单明细")
        CR_DLV_B -->|拣货/出库| CR_DLV_SN_PART("cr_dlv_sn_part<br/>发货序列号/批次")
        
        subgraph "库存扣减"
             direction LR
            CR_DLV_SN_PART -->|更新状态| WM_SN_Out("wm_sn<br/>序列号 810-拣货")
            CR_DLV_SN_PART -->|扣减数量| SZJY_WM_INVENTORY_OUT("szjy_wm_inventory<br/>库存减少")
        end
       
        WM_SN_Storage -->|被拣货| CR_DLV_SN_PART
        SZJY_WM_INVENTORY -->|被扣减| CR_DLV_SN_PART

        CR_DLV_H -- "所有明细完成后" --> SAP_Out_Doc["接口: 销售发货单"]
    end

    PD_PART[("pd_part<br/>物料主数据")]
    
    MO -- "物料信息" --> PD_PART
    CR_DLV_B -- "物料信息" --> PD_PART
```

### 四、 详细业务逻辑关联

#### 1. 入库业务逻辑流

入库流程以"生产批次"为核心，通过状态和表的串联，实现从生产下线到仓库入库的全程跟踪。

1.  **完工送检 (`af_jiaoyantiaoma_finish_io`)**:
    *   **触发**: 生产线操作员使用PDA扫描外箱条码。
    *   **核心表**: `wm_sn`, `mo`, `ea`。
    *   **逻辑**:
        *   系统校验条码状态（必须是'110'-已确认）、类型（必须是'40'-外箱）和产线归属。
        *   校验通过后，在 `me_finish_io_h` 和 `me_finish_io` 中创建完工送检记录。
        *   同时，在 `qm_si_lot_h` 中创建一张待检的检验批，`move_order_no` 字段关联到完工入库单号 `me_finish_io_no`。

2.  **品质检验 (`AX_生产完工检验`)**:
    *   **触发**: 质检员检验产品。
    *   **核心表**: `qm_si_lot_h`。
    *   **逻辑**: 质检员更新 `qm_si_lot_h` 表中的检验结论 `si_conclusion_name` 为"合格"或"特采"。这是下一步仓库接收的先决条件。

3.  **仓库接收 (区分来源)**: 这是入库流程的关键分水岭，根据生产来源 (`me_finish_io_h.workshop_no`) 调用不同的处理逻辑。

    *   **路径 A：本厂产品直接入库 (`af_pda_wms_prod_factory_inbound`)**
        *   **触发**: 仓库管理员扫描本厂生产的、已检验合格的成品外箱。
        *   **核心逻辑**:
            1.  再次确认质检结论为"合格"或"特采"。
            2.  **一步到位**：直接将 `wm_sn` 表中对应序列号（及其内包装）的状态从 '110' 更新为 **'800' (在库)**，并写入正式库位。
            3.  更新 `me_finish_io_h` 的状态 `me_finish_io_rmk4` 为 **"入库完成"**。
        *   **结果**: 产品直接进入可销售库存。

    *   **路径 B：分厂/委外产品暂收 (`af_pda_wms_prod_temp_receipt`)**
        *   **触发**: 仓库管理员扫描江西等分厂运抵的成品外箱。
        *   **核心逻辑**:
            1.  **创建暂收单**: 创建 `wm_temp_receipt` (暂收单) 来管理本次到货。
            2.  **中间状态**: 将 `wm_sn` 序列号状态更新为 **'840' (暂收)**，并放入"暂收库"。此状态的库存**不可销售**。
            3.  **触发二次质检**: 当暂收单收货完成后，系统自动调用 `af_ax_wms_temp_receipt_push_qm_si_lot` 函数。该函数会：
                *   为暂收单的每一种物料，创建一张新的检验批头记录 (`qm_si_lot_h`)。
                *   检验类型 `si_lot_move_type` 被明确设置为 **'315' (完工检验(复检))**。
                *   将暂收单中记录的所有序列号 (`wm_temp_receipt_sn_part`) 关联到新的检验批中 (`qm_si_lot_b_sn`)，等待主厂IQC检验。
                *   更新暂收单 `wm_temp_receipt` 状态为"下推检验单完成"。
            4.  **最终入库**: 待IQC的复检合格后，后续流程会将序列号状态从 '840' (暂收) 正式更新为 '800' (在库)，并移入正式库位。
        *   **结果**: 产品经过两道质检（出厂检+到货检），确保质量后才进入正式库存，流程更严谨。

4.  **SAP集成 (`接口_MES-SAP*`)**:
    *   **触发**: 后台定时任务。
    *   **核心表**: `me_finish_io_h`。
    *   **逻辑**: 接口程序会抓取状态为 **"入库完成"** 的 `me_finish_io_h` 记录，将其转换为SAP需要的格式，并通过API推送到SAP系统生成财务凭证。

