```sql
with
t1 as (select cr_dlv_h_id,cr_dlv_h_no,client_no,cr_dlv_type from cr_dlv_h where cr_dlv_h_rmk6='发货完成' and coalesce(sap_bill_no,'')=''),
t2 as (select * from cr_dlv_b where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t3 as (select * from cr_dlv_sn_part where cr_dlv_h_id in (select cr_dlv_h_id from t1)),
t4 as (select cr_dlv_sn_part_rmk6,lot_no,invp_no,sum(part_qty) as part_qty from t3 group by cr_dlv_sn_part_rmk6,lot_no,invp_no),
t5 as (select t2.cr_dlv_h_id,t2.cr_dlv_b_id,t2.part_no,t2.price,t2.so_h_no,t2.so_b_id,coalesce(t2.cr_dlv_b_rmk5,'0') as ctn_num,
	t4.lot_no,t4.invp_no,t4.part_qty from t2 left join t4 on t4.cr_dlv_sn_part_rmk6=t2.cr_dlv_b_id ),
t6 as (select t1.cr_dlv_h_no,t1.client_no,t1.cr_dlv_type,
	t5.cr_dlv_b_id,t5.part_no,t5.price,t5.so_h_no,t5.so_b_id,t5.invp_no,sum(t5.part_qty) as part_qty_t,t5.ctn_num,json_agg(json_build_object('BatchNo',lot_no,'Qty',t5.part_qty)) as batch_no
	from t1 
	left join t5 on t5.cr_dlv_h_id=t1.cr_dlv_h_id
	group by t1.cr_dlv_h_no,t1.client_no,t1.cr_dlv_type,
	t5.cr_dlv_b_id,t5.part_no,t5.price,t5.so_h_no,t5.so_b_id,t5.invp_no,t5.ctn_num),
t7 as (select cr_dlv_h_no,cr_dlv_type,
	case when cr_dlv_type='订单' then 
	json_agg(json_build_object('BaseType','17','BaseRef',so_h_no,'BaseLine',split_part(so_b_id,'_',2),'WhsCode',invp_no,'PriceAfterVAT',price,'QuanTity',part_qty_t,'BatchNo',batch_no,
	'U_PLqty',ctn_num,'U_OPLANNUM',cr_dlv_h_no,'U_OPLNUM',split_part(cr_dlv_b_id,'_',2),'U_JCDOC',so_h_no,'U_JCLINE',split_part(so_b_id,'_',2)))
	else
	json_agg(json_build_object('ItemCode',part_no,'WhsCode',invp_no,'PriceAfterVAT',price,'QuanTity',part_qty_t,'BatchNo',batch_no,'U_PLqty',ctn_num,'U_basedoc',so_h_no,
	'U_baseline',split_part(so_b_id,'_',2),'U_OPLANNUM',cr_dlv_h_no,'U_OPLNUM',split_part(cr_dlv_b_id,'_',2)))
	end as datas
	from t6
	group by cr_dlv_h_no,cr_dlv_type)

select t7.cr_dlv_h_no,json_build_object('CardCode',t1.client_no,'DocDate',to_char(current_date,'yyyy-mm-dd'),'OwnerCode','379','U_WebNo',t1.cr_dlv_h_no,'details',t7.datas) as datas
from t1 
left join t7 on t7.cr_dlv_h_no=t1.cr_dlv_h_no

```	

```javascript
function main() {
	var success=[];
	var fail=[];

var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	//Log.LogInfo(req_params.me_finish_io_no);
	var cr_dlv_no=req_params.cr_dlv_h_no;

	var params={
		"method": "mes-sap-ODLN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));	
  var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		ReadContext.ExecuteSql("update cr_dlv_h set sap_bill_no='"+result.message+"',upd_time=localtimestamp where cr_dlv_h_no='"+cr_dlv_no+"'");
	}

}

return {"success":success,"fail":fail};
 }
```


逻辑分析：
我来详细分析这个MES-SAP销售发货单接口的功能。

## MES-SAP销售发货单接口分析

### 1. 业务逻辑分析

**功能目标**: 将MES系统中已完成发货的销售订单数据推送到SAP系统，实现销售出库的财务确认和库存扣减。

**业务流程**:
```mermaid
sequenceDiagram
    participant MES as MES销售系统
    participant Warehouse as 仓库系统
    participant Interface as 发货单接口
    participant SAP as SAP系统
    
    MES->>Warehouse: 1. 创建发货单
    Warehouse->>Warehouse: 2. 拣货出库
    Warehouse->>MES: 3. 发货完成确认
    MES->>Interface: 4. 触发SAP推送
    Interface->>SAP: 5. 创建销售发货单(ODLN)
    SAP-->>Interface: 6. 返回SAP发货单号
    Interface->>MES: 7. 更新sap_bill_no
```

**业务价值**:
- 实现销售出库的财务确认
- 同步MES和SAP的库存状态
- 为后续开票和收款提供数据基础

### 2. 数据逻辑分析

#### 复杂的CTE查询结构:
```sql
with
t1 as (-- 获取已发货完成但未推送SAP的发货单头)
t2 as (-- 获取发货单明细)
t3 as (-- 获取发货序列号明细)
t4 as (-- 按批次汇总发货数量)
t5 as (-- 关联明细和批次信息)
t6 as (-- 构建批次JSON并汇总)
t7 as (-- 根据发货类型构建不同的明细JSON)
```

#### 查询条件逻辑:
```sql
-- t1: 发货单头筛选
where cr_dlv_h_rmk6='发货完成' 
  and coalesce(sap_bill_no,'')=''
```

**筛选条件**:
- `cr_dlv_h_rmk6='发货完成'`: 只处理已完成发货的单据
- `sap_bill_no=''`: 避免重复推送到SAP

#### 批次数据聚合逻辑:
```sql
-- t4: 按批次汇总发货数量
select cr_dlv_sn_part_rmk6, lot_no, invp_no, 
       sum(part_qty) as part_qty 
from t3 
group by cr_dlv_sn_part_rmk6, lot_no, invp_no
```

**聚合策略**:
- 按发货明细ID(`cr_dlv_sn_part_rmk6`)分组
- 按批次号(`lot_no`)和库位(`invp_no`)汇总
- 计算每个批次的总发货数量

### 3. 表关联逻辑

```mermaid
graph TD
    A[cr_dlv_h 发货单头] --> B[cr_dlv_b 发货单明细]
    A --> C[cr_dlv_sn_part 发货序列号明细]
    
    B --> D[JSON明细构建]
    C --> E[批次数据聚合]
    E --> D
    
    A -->|cr_dlv_h_id| B
    A -->|cr_dlv_h_id| C
    B -->|cr_dlv_b_id| C
    
    style A fill:#ff9999
    style B fill:#99ccff
    style C fill:#99ff99
```

**核心关联关系**:
- `cr_dlv_h` ← `cr_dlv_b`: 通过 `cr_dlv_h_id` 关联
- `cr_dlv_h` ← `cr_dlv_sn_part`: 通过 `cr_dlv_h_id` 关联  
- `cr_dlv_b` ← `cr_dlv_sn_part`: 通过 `cr_dlv_b_id` 关联(存储在`cr_dlv_sn_part_rmk6`字段)

**数据流转**:
1. 从发货单头获取客户和发货类型
2. 从发货单明细获取物料、价格、销售订单信息
3. 从序列号明细获取实际发货的批次和数量
4. 三表关联形成完整的发货数据

### 4. 发货类型分类逻辑

#### 订单类型发货 (`cr_dlv_type='订单'`):
```json
{
  "BaseType": "17",           // 基于销售订单
  "BaseRef": "so_h_no",       // 销售订单号
  "BaseLine": "订单行号",      // 从so_b_id提取
  "WhsCode": "库位代码",
  "PriceAfterVAT": "含税价格",
  "QuanTity": "发货数量",
  "BatchNo": [批次数组],
  "U_PLqty": "箱数",
  "U_OPLANNUM": "发货单号",
  "U_OPLNUM": "发货行号",
  "U_JCDOC": "销售订单号",
  "U_JCLINE": "销售订单行号"
}
```

#### 非订单类型发货 (其他类型):
```json
{
  "ItemCode": "物料编码",     // 直接指定物料
  "WhsCode": "库位代码", 
  "PriceAfterVAT": "含税价格",
  "QuanTity": "发货数量",
  "BatchNo": [批次数组],
  "U_PLqty": "箱数",
  "U_basedoc": "基础单据号",
  "U_baseline": "基础单据行号",
  "U_OPLANNUM": "发货单号",
  "U_OPLNUM": "发货行号"
}
```

**关键差异**:
- **订单类型**: 使用`BaseType/BaseRef`引用销售订单，SAP自动关联
- **非订单类型**: 直接指定`ItemCode`，需要手动指定物料

### 5. 批次数据处理逻辑

#### 批次JSON构建:
```sql
json_agg(json_build_object('BatchNo',lot_no,'Qty',t5.part_qty)) as batch_no
```

**批次数据结构**:
```json
"BatchNo": [
  {"BatchNo": "20240101001", "Qty": 100},
  {"BatchNo": "20240101002", "Qty": 50},
  {"BatchNo": "20240102001", "Qty": 30}
]
```

**业务意义**:
- 支持一次发货包含多个批次
- 每个批次记录具体发货数量
- 便于SAP系统进行批次库存扣减

### 6. 字段解析与数据提取

#### ID字段解析技术:
```sql
split_part(so_b_id,'_',2)     -- 提取销售订单行号
split_part(cr_dlv_b_id,'_',2) -- 提取发货单行号
```

**ID结构规律**:
- `so_b_id`: 格式为 "前缀_行号"，如 "SO001_1"
- `cr_dlv_b_id`: 格式为 "前缀_行号"，如 "DLV001_1"

#### 箱数处理:
```sql
coalesce(t2.cr_dlv_b_rmk5,'0') as ctn_num
```
- 从发货明细的备注字段5提取箱数
- 默认值为'0'，确保数据完整性

### 7. 代码逻辑分析

#### JavaScript处理流程:
```javascript
// 1. 获取待推送的发货单数据
var Prev = Request.Prev;

// 2. 逐条处理每个发货单
for(var i=0; i<Prev.length; i++) {
    // 3. 构建SAP发货单接口参数
    var params = {
        "method": "mes-sap-ODLN",  // SAP销售发货单接口
        "data": JSON.parse(req_params.datas)
    };
    
    // 4. 调用SAP HTTP接口
    var response = HttpApi.post(host, {body:params});
    
    // 5. 处理返回结果
    if(result.code != 0) {
        // 失败时抛出异常，中断处理
        throw result.message;
    } else {
        // 成功后更新SAP发货单号
        ReadContext.ExecuteSql(
            "update cr_dlv_h set sap_bill_no='" + result.message + 
            "',upd_time=localtimestamp where cr_dlv_h_no='" + cr_dlv_no + "'"
        );
    }
}
```

**错误处理策略**:
- 采用抛出异常的方式，确保数据一致性
- 失败时不更新状态，支持重试机制

### 8. 完整的JSON数据结构

#### 发货单头信息:
```json
{
  "CardCode": "客户编码",
  "DocDate": "2024-01-01",      // 当前日期
  "OwnerCode": "379",           // 固定业务员代码
  "U_WebNo": "发货单号",        // MES发货单号
  "details": [明细数组]
}
```

#### 完整示例:
```json
{
  "CardCode": "CUST001",
  "DocDate": "2024-01-01",
  "OwnerCode": "379", 
  "U_WebNo": "DLV20240101001",
  "details": [{
    "BaseType": "17",
    "BaseRef": "SO20240101001",
    "BaseLine": "1",
    "WhsCode": "WH001",
    "PriceAfterVAT": 100.00,
    "QuanTity": 180,
    "BatchNo": [
      {"BatchNo": "20240101001", "Qty": 100},
      {"BatchNo": "20240101002", "Qty": 80}
    ],
    "U_PLqty": "2",
    "U_OPLANNUM": "DLV20240101001",
    "U_OPLNUM": "1",
    "U_JCDOC": "SO20240101001",
    "U_JCLINE": "1"
  }]
}
```

### 9. 系统集成架构

```mermaid
sequenceDiagram
    participant Sales as 销售系统
    participant MES as MES发货
    participant WMS as 仓库系统  
    participant Interface as 发货单接口
    participant SAP as SAP系统
    participant Finance as 财务系统
    
    Sales->>MES: 1. 创建发货单
    MES->>WMS: 2. 下发拣货任务
    WMS->>WMS: 3. 拣货出库
    WMS->>MES: 4. 发货完成确认
    MES->>Interface: 5. 触发SAP推送
    Interface->>SAP: 6. 创建销售发货单
    SAP-->>Interface: 7. 返回发货单号
    Interface->>MES: 8. 更新SAP单号
    SAP->>Finance: 9. 触发财务处理
```

### 10. 业务场景分析

#### 多批次发货场景:
```mermaid
graph TD
    A[发货单: DLV001] --> B[明细1: 物料A 100个]
    B --> C[批次1: 20240101001 60个]
    B --> D[批次2: 20240101002 40个]
    
    A --> E[明细2: 物料B 50个]
    E --> F[批次3: 20240102001 50个]
```

**处理逻辑**:
- 每个发货明细可包含多个批次
- 系统自动汇总同一明细下的所有批次
- SAP接收后按批次进行库存扣减

#### 订单关联场景:
```mermaid
graph TD
    A[销售订单: SO001] --> B[订单行1: 物料A 100个]
    A --> C[订单行2: 物料B 50个]
    
    B --> D[发货单: DLV001 明细1]
    C --> E[发货单: DLV001 明细2]
    
    D --> F[SAP发货单: 基于SO001行1]
    E --> G[SAP发货单: 基于SO001行2]
```

### 11. 关键技术特点

#### 数据聚合技术:
- **多层CTE**: 逐步构建复杂的数据结构
- **JSON聚合**: `json_agg()`处理批次数组
- **字符串分割**: `split_part()`提取行号信息
- **条件JSON**: `case when`构建不同类型的明细

#### 业务规则处理:
- **发货类型区分**: 订单类型 vs 非订单类型
- **批次管理**: 支持多批次混合发货
- **数量汇总**: 序列号级别到批次级别的汇总
- **状态控制**: 通过状态字段防止重复推送

### 12. 业务价值

1. **财务一体化**: 实现销售出库的财务确认
2. **库存准确性**: 及时同步库存变化到SAP
3. **批次追溯**: 完整的批次流转记录
4. **业务闭环**: 从销售订单到发货完成的完整链路
5. **数据一致性**: 确保MES和SAP数据同步

这个接口是销售出库流程的重要环节，实现了从物理出库到财务确认的业务闭环，是MES-SAP集成中销售侧的核心组件。