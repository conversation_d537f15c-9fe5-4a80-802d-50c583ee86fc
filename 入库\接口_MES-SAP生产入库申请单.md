```sql
select me_finish_io_no,
json_build_object('U_Type','1','U_prccode',workshop_no,'U_prcname',me_finish_io_rmk1,'U_Status','N','U_DueDate',to_char(current_date,'yyyy-mm-dd'),'U_DocDate',to_char(finish_io_datetime,'yyyy-mm-dd'),'U_Note1','','U_Crman','mes','FQCTEMP1',
	case when strpos(mo_no,'F')=1 then json_agg(json_build_object('U_BaseType','分切单','U_SpNum',REPLACE(mo_no,'F',''),'U_SpLine',1,'U_ItemCode',part_no,'U_Quantity',finish_io_qty_ok,'U_OkQTY',finish_io_qty_ok,'U_Batchnum',lot_no)) 
	else json_agg(json_build_object('U_OWdocNum',mo_no,'U_ItemCode',part_no,'U_Quantity',finish_io_qty_ok,'U_OkQTY',finish_io_qty_ok,'U_Batchnum',lot_no)) end ) as datas 
from me_finish_io_h where coalesce(me_finish_io_rmk4,'')=''  and coalesce(sap_inbound_apply_no,'')=''
group by me_finish_io_no
```

```javascript
function main(){
	var success=[];
	var fail=[];
	var Prev=Request.Prev;
	Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	var finish_io_no=req_params.me_finish_io_no;
	Log.LogInfo(req_params.me_finish_io_no);

	var params={
		"method": "mes-sap-Addon",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));

	var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);
	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
	}else{
		success.push(JSON.stringify(result));
		Log.LogInfo(result.message);
		ReadContext.ExecuteSql("update me_finish_io_h set sap_inbound_apply_no='"+result.message+"',upd_time=localtimestamp where me_finish_io_no='"+finish_io_no+"'");
	}
}

return {"success":success,"fail":fail};

}

```