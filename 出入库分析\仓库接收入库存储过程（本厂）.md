```sql
-- DROP FUNCTION public.af_pda_wms_prod_factory_inbound(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_factory_inbound(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库入库（本厂产品）
 * 描述：  
 * 时间：  2024/07
 * 开发者：
 * 数据：  
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_sn_no text;
		_part_no text;
		_invp_area_no text;
		_lot_no text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;		
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_bill_no := json_datas->>'bill_no';	
		_sn_no := json_datas->>'sn_no';

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		--insert into a_test_log values(datas,'wms_prod_factory_inbound',localtimestamp);

		select part_no,lot_no into _part_no,_lot_no from me_finish_io where me_finish_io_no=_bill_no and sn_no=_sn_no;
		select invp_area_no into _invp_area_no from pd_part where part_no=_part_no;
		if coalesce(_invp_area_no, '')='' then
			res := row('false', '此物料无仓库资料，不能入库。');
			return to_json(res);
		end if;
	
		if not exists(select 1 from me_finish_io a where me_finish_io_no=_bill_no and sn_no=_sn_no) then
			_err_msg := format('此产品(标签：【%s】)不属于生产入库单【%s】，不能入库。', _sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id 
			where a.move_order_no=_bill_no and a.si_conclusion_name in ('合格','特采') and b.sn_no=_sn_no) then
			res := row('false', '此产品未检验合格，不能入库');
			return to_json(res);
		end if;
		
		if exists(select 1 from me_finish_io WHERE me_finish_io_no=_bill_no and sn_no=_sn_no and me_finish_io_rmk4='入库完成') then
			res := row('false', '此产品已经入库，不能二次入库。');
			return to_json(res);
		end if;

		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
			res := row('false', '此产品标签状态不正确，不能入库。');
			return to_json(res);
		end if;
	
		update public.wm_sn set sn_status='800',sn_status_name='在库',invp_area_no=_invp_area_no,inventory_lot=_lot_no,
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;

		update public.wm_sn set sn_status='800',sn_status_name='在库',invp_area_no=_invp_area_no,
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_pack_50=_sn_no;

		update public.me_finish_io set me_finish_io_rmk4='入库完成',
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where me_finish_io_no=_bill_no and sn_no=_sn_no;

		if not exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')='') then
			update public.me_finish_io_h set me_finish_io_rmk4='入库完成',
				upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where me_finish_io_no=_bill_no;
		end if;
	
		res := row('true', '仓库入库完成');
		return to_json(res);
	
	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		
	END;
$function$
;
```


逻辑分析：
好的，我们来详细分析一下 `af_pda_wms_prod_factory_inbound` 这个存储过程。从名称和注释可以清晰地看出，它专门用于处理**本厂**生产的成品的正式入库。

### 功能概述

-   **功能名称**: `af_pda_wms_prod_factory_inbound`
-   **核心用途**: 这是一个**直接入库 (Direct-to-Stock)** 的流程。与之前分析的江西分厂的“暂收”流程不同，此功能一步到位，直接将检验合格的本厂产品扫码放入正式仓库，并更新库存状态。
-   **业务场景**: 本厂生产线完工、并经质检判定合格后，仓库人员使用PDA将成品搬运至指定库位，并通过扫描完成系统层面的入库记账。

### 业务逻辑分析 (按执行顺序)

1.  **参数解析**:
    *   接收 `user_no` (用户), `bill_no` (生产完工入库单号), `sn_no` (产品外箱序列号) 作为输入。

2.  **前置条件校验 (Guards)**:
    *   **物料主数据校验**: 通过 `pd_part` 表检查该产品是否设置了默认的入库仓库 (`invp_area_no`)。如果没有设置，则无法入库。
    *   **产品归属校验**: 检查 `me_finish_io` 表，确保当前扫描的 `sn_no` 确实属于 `bill_no` 这张入库单。
    *   **质检结论校验**: 这是最关键的业务门禁。系统会关联 `qm_si_lot_h` (检验批头) 和 `qm_si_lot_b_sn` (检验批序列号)，检查该 `sn_no` 对应的检验结论 `si_conclusion_name` **必须是“合格”或“特采”**。不合格的产品绝对不允许入库。
    *   **重复入库校验**: 检查 `me_finish_io` 中该 `sn_no` 的 `me_finish_io_rmk4` 字段，防止同一产品被重复扫码入库。
    *   **序列号状态校验**: 检查 `wm_sn` 表，确保该 `sn_no` 的当前状态 `sn_status` 是 **'110' (已确认)**。这表示产品已生产完成、等待入库，状态正确。

3.  **核心入库操作 (状态变更)**:
    *   如果所有校验通过，系统执行以下核心更新操作：
        *   **更新外箱状态**: 将 `wm_sn` 表中外箱条码 `_sn_no` 的状态更新为 **'800' (在库)**，并写入正确的库位 `invp_area_no` 和批次号 `inventory_lot`。
        *   **更新内盒状态**: **非常重要的一步**，系统会把外箱 `_sn_no` 所包含的所有内盒/单个产品（通过 `sn_pack_50` 字段关联）的状态**也同步更新为'800' (在库)**。这保证了库存状态的整体一致性。
        *   **更新源单明细状态**: 更新 `me_finish_io` 表中该 `sn_no` 所在行的 `me_finish_io_rmk4` 字段为 **“入库完成”**。
        *   **更新源单头状态**: 检查该 `bill_no` 下是否所有明细行都已“入库完成”。如果是，则将 `me_finish_io_h` 头表的状态也更新为“入库完成”，标志着这张入库单已处理完毕。

### 数据与表逻辑分析

-   **输入核心表**:
    -   `ss_user`: 获取用户信息。
    -   `me_finish_io`: 验证SN归属，获取物料号 `part_no` 和批号 `lot_no`。
    -   `pd_part`: 获取物料的默认入库库位。
    -   `qm_si_lot_h`, `qm_si_lot_b_sn`: 获取质检结论，是流程的核心门禁。
    -   `wm_sn`: 验证序列号当前状态。

-   **输出/变更核心表**:
    -   `wm_sn`: **核心变更表**。序列号（外箱及所有内件）的状态从 '110' (已确认) 变更为 **'800' (在库)**。这是库存增加的直接体现。
    -   `me_finish_io`: 更新明细行状态，用于跟踪单据执行进度。
    -   `me_finish_io_h`: 更新头状态，触发后续的SAP集成等流程。

-   **表间关键链接**:
    -   整个流程由 `sn_no` 和 `bill_no` 驱动。
    -   `me_finish_io` 是枢纽，它连接了入库单 `me_finish_io_h`、序列号 `wm_sn` 和物料 `pd_part`。
    -   `qm_si_lot_h.move_order_no` (关联入库单号) 和 `qm_si_lot_b_sn.sn_no` (关联序列号) 共同构成了质检结果的查询路径。
    -   `wm_sn.sn_pack_50` 字段体现了外箱与内件的父子关系，使得状态可以批量更新。

### 与“暂收”流程的对比与总结

-   **流程更直接**: 这是个一步到位的入库流程，没有“暂收”这个中间环节。
-   **状态变更终态**: 直接将序列号状态置为 **'800' (在库)**，而“暂收”流程是置为 '840' (暂收)。'800' 状态的库存是可供销售出库的。
-   **不产生新单据**: 此流程不创建 `wm_temp_receipt` (暂收单) 等新单据，只是更新现有单据和主数据的状态。
-   **不触发二次质检**: 因为产品是本厂生产且已完成最终检验，入库后无需再触发新的质检任务。

总而言之，`af_pda_wms_prod_factory_inbound` 是一个标准、高效的成品入库功能，它通过严谨的校验确保了只有合格的、信息正确的产品才能进入正式库存，并通过一系列状态更新确保了数据在MES系统内部的闭环。