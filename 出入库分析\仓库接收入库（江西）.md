```sql
-- DROP FUNCTION public.af_pda_wms_prod_temp_receipt(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_prod_temp_receipt(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能：  仓库暂收入库（分厂及委外厂产品）
 * 描述：  分厂及委外厂仅作为一个车间，直接使用生产完工入库单。
 * 时间：  2024/07
 * 开发者：
 * 数据：
 */
	declare
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;

		_bill_no text;
		_bill_id text;

		_sn_no text;
		_temp_receipt_no text;
		_temp_receipt_id text;

		_part_no text;
		_mo_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;

		_part_no_bill text;
		json_result json;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;
		res returntype;
	begin
		json_datas := json(datas);
		_user_no := json_datas->>'user_no';
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';

		insert into a_test_log values(datas,'wms_temp_receipt',localtimestamp);

		-----------2024/09/25 添加同产品多入库单统一暂收 ------------------------------------

		if exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
			select part_no into _part_no_bill from me_finish_io_h where me_finish_io_no=_bill_no;
			if not exists(select distinct 1 from me_finish_io where sn_no=_sn_no and part_no=_part_no_bill) then
				res := row('false', '扫描产品与单据不一致。');
				return to_json(res);
			end if;
			select me_finish_io_no into _bill_no from me_finish_io where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';
			if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
				res := row('false', '此标签对应入库单没有暂收。');
				return to_json(res);
			end if;
		end if;

		-----------2024/09/25 end--------------------------------------------------------------

		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
		select me_finish_io_id_h into _bill_id from me_finish_io_h where me_finish_io_no=_bill_no;
		if not exists(select 1 from me_finish_io where me_finish_io_no=_bill_no and sn_no=_sn_no) then
			_err_msg := format('扫描条码【%s】不属于分厂发货入库申请单【%s】，不能暂收。', _sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		if not exists(select 1 from qm_si_lot_h a left join qm_si_lot_b_sn b on b.si_lot_h_id=a.si_lot_h_id
			where a.move_order_no=_bill_no and a.si_conclusion_name='合格' and b.sn_no=_sn_no and si_lot_move_type='310') then
			res := row('false', '此产品未完工检验合格，不能暂收入库');
			return to_json(res);
		end if;

		if exists(select 1 from wm_temp_receipt a left join wm_temp_receipt_sn_part b on b.temp_receipt_id=a.temp_receipt_id
				where a.delivery_no=_bill_no and b.sn_no=_sn_no) then
			_err_msg := format('此条码【%s】已经暂收，不能二次暂收。', _sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		select part_no,mo_no,finish_io_qty_ok into _part_no,_mo_no,_part_qty from me_finish_io where sn_no=_sn_no and coalesce(me_finish_io_rmk4,'')='';
		----------------------------------------------------------------------------------------------------------------

		if not exists(select 1 from wm_temp_receipt where delivery_no=_bill_no) then
			--
			_temp_receipt_id := af_auid();

			insert into public.wm_temp_receipt
			(temp_receipt_id, temp_receipt_status, temp_receipt_no, temp_receipt_type, delivery_no, factory_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			values(_temp_receipt_id, '110', af_ss_no_generate('wm_temp_receipt_no'), '', _bill_no, 'comlink', '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, '');

			--select dlv_id into _bill_id from me_dlv where dlv_no=_bill_no;
			insert into public.wm_temp_receipt_b
			(temp_receipt_b_id, temp_receipt_id, part_no, part_name, part_spec, part_unit, part_qty_plan, part_qty_real, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _temp_receipt_id, b.part_no, c.part_name, c.part_spec, c.part_unit, a.finish_io_qty_ok, 0, a.mo_no, '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from me_finish_io_h a
			left join mo b on b.mo_no=a.mo_no
			left join pd_part c on c.part_no=b.part_no
			where me_finish_io_no=_bill_no;
		end if;

		select temp_receipt_id,temp_receipt_no into _temp_receipt_id,_temp_receipt_no from wm_temp_receipt where delivery_no=_bill_no;

		select part_qty_plan,part_qty_real into _part_qty_plan,_part_qty_real
		from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;

		if _part_qty_plan < _part_qty_real+_part_qty then
			_err_msg := format('实际暂收数量(已暂收【%s】+现条码【%s】)大于计划(发货单)数量【%s】。', _part_qty_real::int,_part_qty::int,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;

		if _part_qty_plan = _part_qty_real+_part_qty then
			update public.wm_temp_receipt_b set part_qty_real=part_qty_real+_part_qty,temp_receipt_rmk01='暂收完成',upd_time=localtimestamp
			where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;
		else
			update public.wm_temp_receipt_b set part_qty_real=part_qty_real+_part_qty,upd_time=localtimestamp
			where temp_receipt_id=_temp_receipt_id and part_no=_part_no and mo_no=_mo_no;
		end if;

		insert into public.wm_temp_receipt_sn_part
		(temp_receipt_sn_id, temp_receipt_id, sn_no, part_no, part_name, part_spec, part_unit, part_qty, mo_no, temp_receipt_rmk01, temp_receipt_rmk02, temp_receipt_rmk03, temp_receipt_rmk04, temp_receipt_rmk05, temp_receipt_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
		select af_auid(), _temp_receipt_id, _sn_no, _part_no, part_name, part_spec, part_unit, _part_qty, _mo_no, '', '', '', '', '', '', localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
		from pd_part
		where part_no=_part_no;

		---------------------------------------------------------------------------------------------------------------
		update public.wm_sn set sn_status='840',sn_status_name='暂收',invp_no='',invp_area_no='暂收库',
			upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no =_sn_no;

		update public.me_finish_io set me_finish_io_rmk4 ='暂收完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where me_finish_io_no=_bill_no and sn_no=_sn_no;

		if not exists(select distinct 1 from me_finish_io where me_finish_io_no=_bill_no and coalesce(me_finish_io_rmk4,'')<>'暂收完成') then
			update public.me_finish_io_h set me_finish_io_rmk4 ='暂收完成',upd_time=localtimestamp
			where me_finish_io_no=_bill_no;
		end if;
		---------------------------------------------------------------------------------------------------------------
		if not exists(select distinct 1 from wm_temp_receipt_b where temp_receipt_id=_temp_receipt_id and temp_receipt_rmk01='') then
			update public.wm_temp_receipt set temp_receipt_rmk01='暂收完成'
			where temp_receipt_id=_temp_receipt_id;
		end if;

		if exists(select 1 from wm_temp_receipt where temp_receipt_id=_temp_receipt_id and coalesce(temp_receipt_rmk01,'')='暂收完成') then
			json_result := af_ax_wms_temp_receipt_push_qm_si_lot(json_build_object('user_no',_user_no,'host','PDA','selected',json_agg(json_build_object('temp_receipt_no',_temp_receipt_no,'temp_receipt_id',_temp_receipt_id)))::text);
			if json_result->>'successful'='false' then
				res := row('false', json_result->>'msg');
				return to_json(res);
			end if;
		end if;

		res := row('true', '仓库暂收完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN
		GET STACKED DIAGNOSTICS
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;

		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);
	END;
$function$
;

```

逻辑分析：
好的，我们来详细分析一下 `af_pda_wms_prod_temp_receipt` 这个专门用于处理江西分厂产品的“暂收”入库的PostgreSQL存储过程。

### 功能概述

-   **功能名称**: `af_pda_wms_prod_temp_receipt`
-   **核心用途**: 该功能并非标准的最终入库，而是一个**“暂收”**流程。它主要用于处理从分厂（如江西工厂）或委外厂发回的成品。产品进入的不是正式仓库，而是一个临时的“暂收库”，并会触发后续的质检流程。
-   **业务场景**: 当分厂的成品运抵主厂时，需要先进行点收和质量检验，合格后才能正式入库。此存储过程就负责“点收”这一环节。

### 业务逻辑分析 (按执行顺序)

1.  **参数解析与特殊逻辑**:
    *   接收 `user_no` (用户), `bill_no` (生产完工入库单号), `sn_no` (产品外箱序列号) 作为输入。
    *   **智能单据切换**: 这里有一个特殊的业务逻辑（2024/09/25新增）。如果操作员扫描的 `sn_no` 不属于当前 `bill_no`，系统会自动反查此 `sn_no` 真正所属的、并且尚未完成暂收的入库单，然后将操作上下文切换到正确的入库单上。这极大地提升了容错性，避免了因操作员拿错单据而导致的操作中断。

2.  **前置条件校验 (Guards)**:
    *   **产品归属校验**: 验证扫描的 `sn_no` 是否属于（切换后的）入库单 `bill_no`。
    *   **完工检验前置**: 检查 `qm_si_lot_h` 表，确认该 `sn_no` 必须已经有“合格”的**完工检验**结论 (`si_lot_move_type='310'`)。这说明产品在分厂出厂前是合格的。
    *   **重复暂收校验**: 在 `wm_temp_receipt_sn_part` 表中检查，确保该 `sn_no` 没有被重复暂收。

3.  **创建或更新暂收单**:
    *   **首次扫描**: 如果这是该 `bill_no` 的第一个被扫描的条码，系统会：
        1.  创建一个新的**暂收单头** (`wm_temp_receipt`)，状态为'110'（处理中）。
        2.  根据 `me_finish_io_h` 的信息，创建**暂收单明细** (`wm_temp_receipt_b`)，此时已收数量 `part_qty_real` 为0。
    *   **后续扫描**: 如果暂收单已存在，系统：
        1.  获取现有的 `temp_receipt_id`。
        2.  **防超收校验**: 检查“已暂收数量 + 本次暂收数量”是否会超过计划数量。
        3.  更新 `wm_temp_receipt_b` 中的已收数量 `part_qty_real`。

4.  **记录与状态更新**:
    *   **记录序列号**: 将本次扫描的 `sn_no` 插入到 `wm_temp_receipt_sn_part` 表中，这是最明细的暂收记录。
    *   **更新序列号主档**: 将 `wm_sn` 表中该序列号的状态更新为 **'840' (暂收)**，并将其库位信息更新为“暂收库”。这实现了库存的物理隔离和状态锁定。
    *   **更新源单状态**:
        *   更新 `me_finish_io` 明细行中该 `sn_no` 的备注为“暂收完成”。
        *   如果该 `me_finish_io_h` 下所有的SN都已暂收，则将头状态也更新为“暂收完成”。

5.  **触发下游业务**:
    *   **触发二次质检**: 当一个暂收单 `wm_temp_receipt` 下所有明细行都满足“计划数量 = 已收数量”后，系统认为该批次已全部到货。
    *   此时，会自动调用 `af_ax_wms_temp_receipt_push_qm_si_lot` 函数。这个函数的作用是**为这批暂收的货物创建新的质检任务**，让主厂的质检部门（如IQC）进行到货检验。

### 数据与表逻辑分析

-   **输入核心表**:
    -   `me_finish_io_h` / `me_finish_io`: 生产完工入库单，是本次暂收的源单据。
    -   `wm_sn`: 序列号主数据，是操作的核心对象。
    -   `qm_si_lot_h`: 质量检验批，作为前置条件被查询。

-   **输出/变更核心表**:
    -   `wm_temp_receipt` (暂收单头): 整个暂收操作的顶层记录，一个 `delivery_no` (通常是完工入库单号) 对应一张暂收单。
    -   `wm_temp_receipt_b` (暂收单明细): 按物料维度汇总了暂收的计划量和实际已收量。
    -   `wm_temp_receipt_sn_part` (暂收序列号): **核心追溯表**，记录了本次暂收操作中扫描的每一个序列号。
    -   `wm_sn`: 序列号状态从"已确认"或"在途"变更为 **'840' (暂收)**。
    -   `me_finish_io_h` / `me_finish_io`: 状态被更新，标志着源单据的这个环节已处理。

-   **表间关键链接**:
    -   `wm_temp_receipt.delivery_no`  <--> `me_finish_io_h.me_finish_io_no` (暂收单关联源单)
    -   `wm_temp_receipt_sn_part.sn_no` <--> `wm_sn.sn_no` (暂收明细关联序列号主档)
    -   `wm_temp_receipt_sn_part.temp_receipt_id` <--> `wm_temp_receipt.temp_receipt_id` (暂收序列号关联暂收单头)
    -   `af_pda_wms_prod_inbound` (主流程) --> `af_pda_wms_prod_temp_receipt` (调用本过程) --> `af_ax_wms_temp_receipt_push_qm_si_lot` (调用下游质检过程)

### 总结

该存储过程设计了一个严谨、闭环的异地产品暂收流程。它不直接增加正式库存，而是将货物引入一个待检的中间状态，并通过状态机（`wm_sn.sn_status`）和触发下游流程（创建新质检批）来确保业务流程的完整性。这种设计在多工厂协同生产的模式下至关重要，能有效管控异地产品的入库质量。