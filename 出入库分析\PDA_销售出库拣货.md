```sql
-- DROP FUNCTION public.af_pda_wms_sales_outbound_picking_2(varchar);

CREATE OR REPLACE FUNCTION public.af_pda_wms_sales_outbound_picking_2(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
/*
 * 功能   ： 销售出库拣货
 * 描述   ： 取消仓库校验
 * 时间   ：
 * 开发者 ：
 */
	declare 
		json_datas json;
		_user_id text;
		_user_no text;
		_user_name text;
		_host text;
	
		_bill_no text;
		_bill_id text;
		_sn_no text;
		_part_no text;
		_part_qty numeric;
		_part_qty_plan numeric;
		_part_qty_real numeric;
	
		_sn_type text;
		_produce_date date;
		_produce_date_min date;
	
		sub_datas record;
		_prod_lot text;
		_b_id text;
		_invp_area text;
		_client_no text;
		_client_part_no text;

		_prod_cycle text;
		_prod_cycle_min text;

		_err_msg_text text;
		_err_pg_detail text;
		_err_msg text;	
		res returntype;
	begin
		json_datas := json(datas);	
		_user_no := json_datas->>'user_no';
	
		json_datas := json(json_datas->'datas'->0);
		_bill_no := json_datas->>'bill_no';
		_sn_no := json_datas->>'sn_no';
		--_part_no := json_datas->>'part_no';	
	
		select user_id,user_name into _user_id,_user_name from ss_user where user_no=_user_no;
	
		select part_no,part_qty,sn_type,inventory_lot,produce_date::date,invp_area_no into _part_no,_part_qty,_sn_type,_prod_lot,_produce_date,_invp_area  
		from wm_sn where sn_no=_sn_no;
	
		if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
			_err_msg := format('扫描销售出库单【%s】，不是待拣货状态。',_bill_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		select cr_dlv_h_id,client_no into _bill_id,_client_no from cr_dlv_h where cr_dlv_h_no=_bill_no;

		if _client_no='3031' or _client_no='3039' then
			_client_part_no := substring(split_part(_sn_no,'/',1),3);
			if substring(_part_no,length(_client_part_no)+2,4)<> _client_no then
				_part_no := substring(_part_no,1,length(_client_part_no)+1)||_client_no||substring(_part_no,length(_client_part_no)+6);
			end if;
		end if;


		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no) then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】）不属于此销售出库单【%s】限定的产品，不能拣货。',_part_no,_sn_no,_bill_no);
			res := row('false', _err_msg);
			return to_json(res);		
		end if;
	
		if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='800') then
			_err_msg := format('扫描产品（物料编码【%s】条码【%s】）不是在库状态，不能拣货。',_part_no,_sn_no);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
		
		----------------------------------------------------------------------------------------
		if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
			select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
			--if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and produce_date::date<_produce_date and sn_status='800') then
			if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
				--select min(produce_date)::date into _produce_date_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';
				select min(substring(split_part(_sn_no,'/021748',2),1,4)) into _prod_cycle_min from wm_sn where part_no=_part_no and sn_type=_sn_type and sn_status='800';				
				_err_msg := format('扫描产品（物料编码【%s】条码【%s】日期【%s】），存在更早日期的产品【%s】，不能拣货。',_part_no,_sn_no,_prod_cycle,_prod_cycle_min);
				res := row('false', _err_msg);
				return to_json(res);
			end if;
		end if;
		----------------------------------------------------------------------------------------
	
		if exists(select 1 from cr_dlv_sn_part where sn_no=_sn_no) then
			res := row('false', '扫描产品条码已经拣货，不能二次拣货。');
			return to_json(res);
		end if;
	
		select sum(part_qty) into _part_qty_real from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and part_no=_part_no;
		select sum(cr_dlv_qty_plan) into _part_qty_plan from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no;
		if _part_qty_plan < coalesce(_part_qty_real,0)+_part_qty then
			_err_msg := format('产品（物料编码【%s】）拣货数量(已拣货数量【%s】+现拣货数量【%s】)大于销售出库单【%s】需要求数量【%s】，不能拣货。',_part_no,coalesce(_part_qty_real,0),_part_qty::int,_bill_no,_part_qty_plan::int);
			res := row('false', _err_msg);
			return to_json(res);
		end if;
	
		---------------------------------------------------------------------------------------
		if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no having count(*)>1) then
			for sub_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no order by cr_dlv_b_id) loop
				if 	sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty >0 and _part_qty>0 then	
					if (sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty)>=_part_qty then
						update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := 0;
					else
						update public.cr_dlv_b set cr_dlv_qty=sub_datas.cr_dlv_qty_plan
						where cr_dlv_b_id=sub_datas.cr_dlv_b_id;

						insert into public.cr_dlv_sn_part
						(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
						select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', sub_datas.cr_dlv_qty_plan-sub_datas.cr_dlv_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
						from pd_part
						where part_no=_part_no;

						_part_qty := _part_qty+sub_datas.cr_dlv_qty-sub_datas.cr_dlv_qty_plan;
					end if;
				end if;

			end loop;
		else
			select cr_dlv_b_id into _b_id from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no;
			insert into public.cr_dlv_sn_part
			(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, part_unit_name, part_idt, mrp_region_no, lot_no, invp_no, invp_name, part_qty, cr_dlv_sn_part_rmk1, cr_dlv_sn_part_rmk2, cr_dlv_sn_part_rmk3, cr_dlv_sn_part_rmk4, cr_dlv_sn_part_rmk5, cr_dlv_sn_part_rmk6, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host)
			select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', _b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
			from pd_part
			where part_no=_part_no;

			update public.cr_dlv_b set cr_dlv_qty=cr_dlv_qty+_part_qty
			where cr_dlv_b_id=_b_id;
		end if;
		---------------------------------------------------------------------------------------

		update public.cr_dlv_b set cr_dlv_b_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where cr_dlv_h_id=_bill_id and part_no=_part_no and cr_dlv_qty_plan=cr_dlv_qty;
		
		---------------------------------------------------------------------------------------

		if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_b_rmk6,'')='') then
			update public.cr_dlv_h set cr_dlv_h_rmk6='拣货完成',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
			where cr_dlv_h_id=_bill_id; 

			update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
			from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
				from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
				left join (select part_no,count(sn_no) as ctn_num from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id) group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
				where tmp1.rn=1) tt
			where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
		end if;
	
		update public.wm_sn set sn_status='810',sn_status_name='拣货',upd_time=localtimestamp,upd_user=_user_id,upd_user_no=_user_no,upd_user_name=_user_name
		where sn_no=_sn_no;
		---------------------------------------------------------------------------------------
		res := row('true', '销售出库拣货完成');
		return to_json(res);

	EXCEPTION WHEN OTHERS THEN 
		GET STACKED DIAGNOSTICS 
			_err_msg_text = MESSAGE_TEXT,
			_err_pg_detail = PG_EXCEPTION_DETAIL;
	
		_err_msg := format('错误信息:%s,详情:%s',_err_msg_text,_err_msg_text);
		res := row('false',_err_msg);
		return to_json(res);		

	END;
$function$
;

```


功能逻辑：



          
## PDA销售出库拣货功能逻辑分析

这个功能是一个PostgreSQL存储过程，用于处理有序列号管控产品的销售出库拣货操作。以下是详细的业务逻辑分析：

### 功能概述
- **功能名称**：`af_pda_wms_sales_outbound_picking_2`
- **主要用途**：处理需要序列号管控的产品销售出库拣货
- **特殊说明**：取消仓库校验（从描述中可以看出这是一个优化版本）
- **适用场景**：高价值产品、需要追溯的商品等需要序列号跟踪的商品

### 核心业务逻辑

#### 1. 参数解析与基础验证
```sql
json_datas := json(datas);
_user_no := json_datas->>'user_no';
_bill_no := json_datas->>'bill_no';
_sn_no := json_datas->>'sn_no';
```
- 解析输入的JSON参数，获取用户编号、发货单号和序列号
- 通过`ss_user`表验证用户身份
- 通过`wm_sn`表获取序列号对应的产品信息

#### 2. 发货单状态验证
```sql
if not exists(select 1 from cr_dlv_h where cr_dlv_h_no=_bill_no and coalesce(cr_dlv_h_rmk6,'')='') then
    _err_msg := format('扫描销售出库单【%s】，不是待拣货状态。',_bill_no);
end if;
```
- 检查发货单是否存在且处于待拣货状态
- `cr_dlv_h_rmk6`字段为空表示待拣货状态

#### 3. 特殊客户物料编码处理
```sql
if _client_no='3031' or _client_no='3039' then
    _client_part_no := substring(split_part(_sn_no,'/',1),3);
    if substring(_part_no,length(_client_part_no)+2,4)<> _client_no then
        _part_no := substring(_part_no,1,length(_client_part_no)+1)||_client_no||substring(_part_no,length(_client_part_no)+6);
    end if;
end if;
```
- 针对特定客户（3031、3039）进行物料编码转换
- 从序列号中提取客户物料编码，动态调整物料编码格式

#### 4. 产品归属验证
```sql
if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no) then
    _err_msg := format('扫描产品（物料编码【%s】条码【%s】）不属于此销售出库单【%s】限定的产品，不能拣货。',_part_no,_sn_no,_bill_no);
end if;
```
- 验证扫描的产品是否属于当前发货单

#### 5. 序列号状态验证
```sql
if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='800') then
    _err_msg := format('扫描产品（物料编码【%s】条码【%s】）不是在库状态，不能拣货。',_part_no,_sn_no);
end if;
```
- 验证序列号状态必须是'800'（在库状态）

#### 6. FIFO（先进先出）管控
```sql
if exists(select 1 from pd_part where part_no=_part_no and is_fifo=true) then
    select substring(split_part(_sn_no,'/021748',2),1,4) into _prod_cycle;
    if exists(select distinct 1 from wm_sn where part_no=_part_no and sn_type=_sn_type and substring(split_part(sn_no,'/021748',2),1,4) < _prod_cycle and sn_status='800') then
        _err_msg := format('扫描产品（物料编码【%s】条码【%s】日期【%s】），存在更早日期的产品【%s】，不能拣货。',_part_no,_sn_no,_prod_cycle,_prod_cycle_min);
    end if;
end if;
```
- 对于设置了FIFO管控的产品，检查是否存在更早生产日期的产品
- 从序列号中解析生产周期信息（通过'/021748'分隔符）
- 强制按生产日期顺序出库

#### 7. 重复拣货检查
```sql
if exists(select 1 from cr_dlv_sn_part where sn_no=_sn_no) then
    res := row('false', '扫描产品条码已经拣货，不能二次拣货。');
end if;
```
- 防止同一序列号被重复拣货

#### 8. 数量控制验证
```sql
select sum(part_qty) into _part_qty_real from cr_dlv_sn_part where cr_dlv_h_id=_bill_id and part_no=_part_no;
select sum(cr_dlv_qty_plan) into _part_qty_plan from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no;
if _part_qty_plan < coalesce(_part_qty_real,0)+_part_qty then
    _err_msg := format('产品（物料编码【%s】）拣货数量(已拣货数量【%s】+现拣货数量【%s】)大于销售出库单【%s】需要求数量【%s】，不能拣货。',_part_no,coalesce(_part_qty_real,0),_part_qty::int,_bill_no,_part_qty_plan::int);
end if;
```
- 检查拣货数量不能超过计划数量
- 计算已拣货数量 + 当前拣货数量 ≤ 计划数量

#### 9. 多明细行处理逻辑

**情况1：同一物料有多个明细行**
```sql
if exists(select 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no having count(*)>1) then
    for sub_datas in (select * from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no order by cr_dlv_b_id) loop
        -- 按明细行顺序分配拣货数量
    end loop;
end if;
```
- 按明细行ID顺序处理
- 优先满足第一个明细行，再处理后续明细行
- 支持跨明细行分配

**情况2：单一明细行**
```sql
else
    select cr_dlv_b_id into _b_id from cr_dlv_b where cr_dlv_h_id=_bill_id and part_no=_part_no;
    -- 直接更新该明细行
end if;
```

#### 10. 拣货记录生成
```sql
insert into public.cr_dlv_sn_part
(cr_dlv_sn_part_id, cr_dlv_h_id, sn_no, part_no, part_name, part_spec, part_unit, ...)
select af_auid(), _bill_id, _sn_no, _part_no, part_name, part_spec, part_unit, '', '', '', _prod_lot, _invp_area, '', _part_qty, '', '', '', '', '', sub_datas.cr_dlv_b_id, localtimestamp, _user_id, _user_no, _user_name, '', localtimestamp, _user_id, _user_no, _user_name, ''
from pd_part where part_no=_part_no;
```
- 在`cr_dlv_sn_part`表中记录拣货明细
- 记录完整的序列号、批次、仓库等信息
- 关联具体的发货明细行

#### 11. 状态更新机制

**明细行状态更新**：
```sql
update public.cr_dlv_b set cr_dlv_b_rmk6='拣货完成'
where cr_dlv_h_id=_bill_id and part_no=_part_no and cr_dlv_qty_plan=cr_dlv_qty;
```
- 当明细行计划数量=实际数量时，标记为拣货完成

**发货单头状态更新**：
```sql
if not exists(select distinct 1 from cr_dlv_b where cr_dlv_h_id=_bill_id and coalesce(cr_dlv_b_rmk6,'')='') then
    update public.cr_dlv_h set cr_dlv_h_rmk6='拣货完成';
end if;
```
- 当所有明细行都拣货完成时，更新发货单头状态

**箱数统计更新**：
```sql
update cr_dlv_b set cr_dlv_b_rmk5=tt.ctn_num::text
from (select tmp1.cr_dlv_h_id,tmp1.cr_dlv_b_id,coalesce(tmp2.ctn_num,0) as ctn_num
    from (select cr_dlv_b_id,cr_dlv_h_id,part_no, row_number() over(partition by part_no) as rn from cr_dlv_b where cr_dlv_h_id =_bill_id) tmp1
    left join (select part_no,count(sn_no) as ctn_num from (select distinct part_no,sn_no from cr_dlv_sn_part where cr_dlv_h_id =_bill_id) group by part_no) tmp2 on tmp2.part_no=tmp1.part_no
    where tmp1.rn=1) tt
where cr_dlv_b.cr_dlv_b_id=tt.cr_dlv_b_id;
```
- 统计每个物料的拣货序列号数量作为箱数
- 更新到明细行的`cr_dlv_b_rmk5`字段

**序列号状态更新**：
```sql
update public.wm_sn set sn_status='810',sn_status_name='拣货'
where sn_no=_sn_no;
```
- 将序列号状态从'800'（在库）更新为'810'（拣货）

### 关键特点

1. **序列号级别管控**：每个产品都有唯一序列号，实现精确追溯
2. **FIFO强制执行**：对于设置FIFO的产品，强制按生产日期顺序出库
3. **多明细行智能分配**：支持同一物料在多个明细行中的智能分配
4. **客户特殊处理**：针对特定客户的物料编码转换逻辑
5. **完整状态跟踪**：从在库→拣货的完整状态流转
6. **防重复操作**：多重检查防止重复拣货
7. **取消仓库校验**：相比早期版本，简化了仓库验证流程

### 与无序列号出库的区别

- **精确到序列号**：每次操作针对具体序列号
- **FIFO管控**：强制先进先出，确保产品新鲜度
- **状态跟踪**：序列号状态的精确管理
- **复杂验证**：更多的业务规则验证
- **客户定制**：支持客户特殊的编码规则

### 异常处理
- 完整的PostgreSQL异常处理机制
- 详细的错误信息返回
- 事务回滚保证数据一致性

这个功能设计非常完善，既保证了序列号级别的精确管控，又考虑了各种复杂的业务场景，是一个成熟的WMS拣货功能实现。
        