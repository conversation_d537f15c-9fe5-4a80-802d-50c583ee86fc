```sql


CREATE OR REPLACE FUNCTION public.af_jiaoyantiaoma_finish_io(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
/*
---扫描前校验   select af_sales_notice_bf('k')  af_jiaoyantiaoma 
*/		 

declare
    _datas json;
     _results returntype; 
    _sn_no varchar;  
    _re_datas varchar; 
    _mo_no text;
    _mo_no_f text;
    _ea_no text;
    _mo_parent_top text;
    _mo_ea_no text;
    _sn_status_name text;
    _sn_type text;
    _sn_type_name text;

	_part_no_sn text;
	_part_no_mo text;
   
   	_si_lot_h_id	text;
   	_si_lot_h_no	text;
   	_s_sn_no	text;
    tmp	json;
   
begin  
  		--- 
	 _datas:=json(datas); 
	-- _re_datas:=(_datas->'datas')::varchar; 
    _sn_no:=_datas->'datas'->0->>'sn_no';
   _ea_no:=_datas->'datas'->0->>'ea_no';
    raise notice '%',_sn_no;
   
  -- insert into a_test_log
  --	 select datas,'af_jiaoyantiaoma_finish_io',now();
		if (coalesce(_sn_no,'') = '') then 
    	_results:=row('false','外箱条码不能为空！');
   		return  to_json(_results);  
    end if; 
   
   	select ws.mo_no,part_no into _mo_no,_part_no_sn from wm_sn ws where ws.sn_no =_sn_no ;
    
    select m.workshop_no,m.mo_parent_top,part_no into _mo_ea_no,_mo_parent_top,_part_no_mo  from mo m where m.mo_no = _mo_no;
	if coalesce(_part_no_sn,'part_no_sn')<>coalesce(_part_no_mo,'part_no_mo') then
		_results := row('false','标签的物料编码与工单的物料编码不一致。');
		return to_json(_results);
	end if;
		
		if not exists(select 1 from wm_sn where sn_no=_sn_no)   then 
    	_results:=row('false','外箱条码:'||_sn_no||'不存在！');
   		return  to_json(_results);  
    end if; 
   
    if not exists(select 1 from wm_sn where sn_no=_sn_no and wm_sn.sn_type ='40'  )   then 
			select sn_type,sn_type_name from into _sn_type,_sn_type_name wm_sn where sn_no=_sn_no;
    	_results:=row('false','条码类型:['||_sn_type|| '-' || _sn_type_name ||']不是外箱类型！');
   		return  to_json(_results);  
    end if; 
   
	if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
		select sn_status_name into _sn_status_name from wm_sn where sn_no=_sn_no;
		_results := row('false', '外箱条码状态:【'||_sn_status_name||'】不是确认状态(110),不允许完工送检');
		return to_json(_results);
	end if;
   --判断外箱条码是否已包装完成  pack_qty_used
   	if exists(select 1 from wm_sn where sn_no=_sn_no and coalesce (wm_sn.pack_qty_used,0) <> wm_sn.part_qty )   then 
    	_results:=row('false','外箱条码:'||_sn_no||'未包装完成，不允许完工送检！');
   		return  to_json(_results);  
    end if; 
   
    --判断是否江西产线2023-05-31 增加委外分厂产线校验
   	if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%')then
   		if not exists(select 1 from mo_rmb b
						left join mo_rmb_ea e on e.unode = b.unode 
						left join ea a on a.ea_id = e.ea_id 
						where b.mo_no = _mo_no and a.ea_no = _ea_no)then
			_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);
   		end if;
   	else
		--条码工单对应的产线编号与参数产线是否一致验证；
	    if  _ea_no <> coalesce(_mo_ea_no,'_mo_ea_no') then 
	    	_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);  
	    end if; 
   	end if;
   
    
   
   
   /* if not exists(select 1 from mo m where m.mo_no =_mo_no and m.mo_status in ('300','310') )   then 
    	_results:=row('false','生产订单：'||_mo_no||'不存在或状态已结案！');
   		return  to_json(_results);  
    end if; */
   	--判断条码是否存在送检记录
    if  exists(select 1 	from me_finish_io a ,me_finish_io_h h 
   				where a.me_finish_io_no = h.me_finish_io_no  
   				and a.sn_no = _sn_no and h.finish_io_status<>'230')   then 
   				--2023-05-17判断是否重工单
   				_mo_no_f := substring(_mo_no,1,1);
   				if(_mo_no_f = 'F')then
				   		--重工单校验：重工单+条码是否存在送检记录
				   		if exists(select 1 	from me_finish_io a ,me_finish_io_h h 
				   				where a.me_finish_io_no = h.me_finish_io_no  
				   				and a.sn_no = _sn_no and h.finish_io_status<>'230' and a.mo_no = _mo_no)then
				   				_results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
								return  to_json(_results);
				   		end if;
				 else
				        _results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
						return  to_json(_results);
				 end if;
		    	  
   	end if; 
   
   
   --判断条码是否在过程检验判定不合格2023-08-03
   if exists(select 1 from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格')then
   		select si_lot_h_id ,si_lot_h_no into _si_lot_h_id,_si_lot_h_no from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格';
   		if exists(select 1 from szjy_oqc_gc_disqualified d where d.si_lot_h_no = _si_lot_h_no and d.mo_no = _mo_no and d.disqualified_si_conclusion_name = '批退')then
   			--获取外箱绑定的条码
   			select array_to_json(array_agg(row_to_json(t))) into tmp
   			from(
   				select s.sn_no from wm_sn s where s.sn_pack_50  = _sn_no and s.sn_type = '00'
   			)t;
   			--循环外箱绑定的产品条码
   			for icount in 0..json_array_length(tmp)-1 loop
	   			_s_sn_no :=tmp->icount->>'sn_no';
   				if exists(select 1 from qm_si_lot_b_sn b where b.si_lot_h_id = _si_lot_h_id and b.sn_no = _s_sn_no)then
   					_results:=row('false','产品条码'||_s_sn_no||' 在OQC过程检验判定为不合格，不能进行完工入库送检，请确认！');
					return  to_json(_results);
   				end if;
   			end loop;
   			
   		end if;
   		
   end if;
   
   

   _results:=row('true','扫码成功','[{"sn_no":"'||_sn_no||'"}]');
	return  to_json(_results);  
	END;
$function$
;

```

```SQL
-- DROP FUNCTION public.af_me_finish_io_h_scan(varchar);

CREATE OR REPLACE FUNCTION public.af_me_finish_io_h_scan(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
 ---分三步　      　第一　插入　条码表　
 --         第二　 插入　出库表身 　 
 --         第三     插入　出库表头  
declare
    _datas json;
    results returntype;
   _user_no varchar;
   _scan json;
	_cr_dlv_h_id  varchar ;  -- 发货单 
    _cr_dlv_h_no  varchar; -- 通知单
   _time timestamp;  
    _today date;  
   _xsck  varchar; 
	  rb record; 
	 _tday text;   
	_ea_no text;
	_mo_no text;
   _user_id text;
   _user_name text;
  _me_finish_io_id_h text;
  _ea_name text;
  _me_finish_io_no text;
  _si_lot_h_id	text;
  _si_lot_h_no text;
 
_lot_no text;

_prod_cycle_cnt int;

	_part_qty numeric;
 	row_datas record;	

begin
	_time:=now();  
	_cr_dlv_h_id:=af_auid();   
	_tday:=to_char(current_date,'yyyy-mm-dd');  	    
             
	_datas := json(datas);
	_scan:=_datas->'datas';
	_user_no:=_datas->>'user_no';
	 -- _mo_no:=_scan->0->>'mo_no';

	raise notice '_time  %, _id  %, _user_no  % ,  _cr_dlv_h_no  %',
                 _time,_cr_dlv_h_id,_user_no,_cr_dlv_h_no  ; 
      
     select su.user_id ,su.user_name into _user_id,_user_name from ss_user su where su.user_no =_user_no; 

	create temp table temp_table_scwgsj as (select a.ea_no,a.sn_no,b.mo_no,b.part_no,b.part_qty  from json_to_recordset(json(_scan))as a(sn_no text, ea_no text)
											left join wm_sn b on b.sn_no=a.sn_no);

	----------2025/04/07 增加不同周期产品分开提交的管控(仅支持华为产品)-------------------------------------------
	select count(prod_cycle) into _prod_cycle_cnt
	from (select distinct substring(split_part(sn_no,'/021748',2),1,4) as prod_cycle from temp_table_scwgsj) tt;
	if _prod_cycle_cnt>1  then
		results := row('false', format('提交生产入库的产品存在 %s 个周期，请分开提交.', _prod_cycle_cnt));
		return to_json(results);
	end if;
	----------------------------------------------------

	for row_datas in (select distinct ea_no, mo_no, part_no from temp_table_scwgsj) loop

		select sum(part_qty) into _part_qty from temp_table_scwgsj where mo_no=row_datas.mo_no;
		select e.ea_name into _ea_name  from ea e where e.ea_no = row_datas.ea_no ;  

    	_me_finish_io_id_h:=af_auid();
    	_si_lot_h_id:=af_auid();
    	_me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    	_si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);

		_lot_no := concat('M',to_char(current_date,'YYMMDD'),row_datas.mo_no);
		--写入库单表头    
    	insert into public.me_finish_io_h
			(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
			mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
			invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
			invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
			me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
			fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, mo_type)
		values ( 
			_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
			row_datas.mo_no, row_datas.part_no, '', '', '', '', _lot_no, _part_qty, 
			'', 0, '', 0, '', 0, 
			'', row_datas.ea_no, _user_no, _user_name, 
			_ea_name, '20', '', '', 
			'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
			false, 0, null, '');

     	---写入表明细
     	insert into public.me_finish_io
			(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
			part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
			finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
			crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			io_is_sucessed, io_times, io_last_time, sn_no)		
		select af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,pp.part_spec ,
			pp.part_unit ,'',_lot_no,ws.part_qty ,'',0,'',0,'',
			0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
		from temp_table_scwgsj a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit
		where a.mo_no=row_datas.mo_no;
	

 		insert into qm_si_lot_h(
			si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
			part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
			move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
			order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
			si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
			si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
			si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
			si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
			da_switch_id,ea_no,ea_name,
			crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
			upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
			io_is_sucessed,io_times,io_last_time)
		select 
			_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE',m.factory_no,sf.factory_name,m.part_no,p.part_name,
			p.part_spec,null,'','',_part_qty,'310','完工检验',null,null,
			_me_finish_io_id_h,_me_finish_io_no,'MO','生产订单',_me_finish_io_id_h,null,null,m.mo_no ,m.client_no,
			m.client_name,null,null,'20','抽检','20','正常','10','一般','',0,0,
			null,null,false,null,null,
			null,null,null,null,null,null,
			null,null,null,null,null,null,
			null,row_datas.ea_no,_ea_name,
			now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			false,0,null
		from (select * from mo where mo_no=row_datas.mo_no) m
		left join ss_factory sf on sf.factory_no = m.factory_no
		left join pd_part p on p.part_no=m.part_no;


		--写入送检单条码明细
		INSERT INTO public.qm_si_lot_b_sn
			(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
			si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
			si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
			upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
			sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
			mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
			sn_type,sn_type_name)
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		left join wm_sn ws on a.sn_no = ws.sn_no
		where a.mo_no=row_datas.mo_no 	
		union all 
		select 
			af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
			null,null,null,null,null,
			null,now(),_user_id,_user_no,_user_name,'pda',
			now(),_user_id,_user_no,_user_name,'pda',
			ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
			ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
			ws.sn_type,ws.sn_type_name
		from temp_table_scwgsj a
		join wm_sn ws on a.sn_no = ws.sn_pack_50
		where a.mo_no=row_datas.mo_no;

	end loop;

/*   -----2024/09/03 disable----------------------
	  _ea_no:=_scan->0->>'ea_no';
	 
	 
	raise notice '_time  %, _id  %, _user_no  % ,  _cr_dlv_h_no  %',
                 _time,_cr_dlv_h_id,_user_no,_cr_dlv_h_no  ; 
      
     select su.user_id ,su.user_name into _user_id,_user_name from ss_user su where su.user_no =_user_no;  
     select e.ea_name into _ea_name  from ea e where e.ea_no = _ea_no ;   
    _me_finish_io_id_h:=af_auid();
    _si_lot_h_id:=af_auid();
    _me_finish_io_no:=af_ss_no_generate('finish_io_no'::character varying);
    _si_lot_h_no:=af_ss_no_generate('wgjy_si_lot_h_no'::character varying);
   
    if exists (select  1             
			 from json_to_recordset(json(_scan))as a(sn_no varchar)) then 
  
   --写入库单表头    
    INSERT INTO public.me_finish_io_h
		(me_finish_io_id_h, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, 
		mo_no, part_no, part_name, part_spec, part_unit, part_idt, lot_no, finish_io_qty_ok, 
		invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, finish_io_qty_other, 
		invp_no_other, workshop_no, workshop_worker_no, workshop_worker_name, 
		me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, 
		fb_id, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
		upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
		io_is_sucessed, io_times, io_last_time, mo_type)
	values ( 
	_me_finish_io_id_h, _me_finish_io_no, '200', now(), 'comlink', 
	'', '', '', '', '', '', '', 0, 
	'', 0, '', 0, '', 0, 
	'', _ea_no, _user_no, _user_name, 
	_ea_name, '20', '', '', 
	'', now(),_user_id,_user_no,_user_name,'pda',now(),_user_id,_user_no,_user_name,'pda',
   false, 0, null, '');

    create temp table temp_table_scwgsj as (select  distinct  a.sn_no  from json_to_recordset(json(_scan))as a(sn_no varchar));	
     ---写入表明细
     INSERT INTO public.me_finish_io
		(me_finish_io_id, me_finish_io_no, finish_io_status, finish_io_datetime, factory_no, mo_no, part_no, part_name, part_spec, 
		part_unit, part_idt, lot_no, finish_io_qty_ok, invp_no_ok, finish_io_qty_ng, invp_no_ng, finish_io_qty_scrap, invp_no_scrap, 
		finish_io_qty_other, invp_no_other, me_finish_io_rmk1, me_finish_io_rmk2, me_finish_io_rmk3, me_finish_io_rmk4, fb_id, 
		crt_time, crt_user, crt_user_no, crt_user_name, crt_host, upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
		io_is_sucessed, io_times, io_last_time, sn_no)
		
		select 
		af_auid(), _me_finish_io_no, '210', now(),'comlink',ws.mo_no ,ws.part_no ,ws.part_name ,ws.part_spec ,
		pp.part_unit ,'','',ws.part_qty ,'',0,'',0,'',
		0,'','','','','','',now(),_user_id,_user_no,_user_name,'pda', now(),_user_id,_user_no,_user_name,'pda',
		false, 0, null, ws.sn_no 
	from temp_table_scwgsj a
		left join wm_sn ws on ws.sn_no = a.sn_no 
		left join pd_part pp on pp.part_no = ws.part_no 
		left join av_ss_qty_unit asqu on asqu.qty_unit_no = pp.part_unit 
		;
	
	---生成检验单
 	insert into qm_si_lot_h(
		si_lot_h_id,si_lot_h_no,si_lot_h_status,factory_no,factory_name,part_no,part_name,
		part_spec,part_idt,wkp_no,wkp_name,si_lot_qty,si_lot_move_type,si_lot_move_type_name,
		move_order_h_id,move_order_b_id,move_order_id,move_order_no,order_type,order_type_name,
		order_h_id,order_b_id,order_id,order_no,client_no,client_name,supplier_no,supplier_name,
		si_type,si_type_name,si_degree,si_degree_name,si_level,si_level_name,si_aql,si_lot_qty_ok,
		si_lot_qty_ng,si_conclusion_no,si_conclusion_name,si_is_pass,si_lot_h_rmk01,si_lot_h_rmk02,
		si_lot_h_rmk03,si_lot_h_rmk04,si_lot_h_rmk05,si_lot_h_rmk06,si_lot_h_rmk07,si_lot_h_rmk08,
		si_lot_h_rmk09,si_lot_h_rmk10,si_lot_h_rmk11,si_lot_h_rmk12,si_lot_h_rmk13,si_lot_h_rmk14,
		da_switch_id,ea_no,ea_name,
		crt_time,crt_user,crt_user_no,crt_user_name,crt_host,
		upd_time,upd_user,upd_user_no,upd_user_name,upd_host,
		io_is_sucessed,io_times,io_last_time
	)
	select 
		_si_lot_h_id,_si_lot_h_no,'5B8A5FD43EEB00004DFE'as si_lot_h_status,m.factory_no,sf.factory_name,ws.part_no,ws.part_name,
		ws.part_spec,null,ws.wkp_no ,ws.wkp_name ,coalesce(sum(ws.part_qty),0) si_lot_qty,'310'as si_lot_move_type,'完工检验'as si_lot_move_type_name,null as move_order_h_id,null as move_order_b_id,
		_me_finish_io_id_h as move_order_id,_me_finish_io_no,'MO'as order_type,'生产订单'as order_type_name,_me_finish_io_id_h,null as order_b_id,null as order_id,m.order_no1 ,m.client_no,
		m.client_name,null as supplier_no,null as supplier_name,'20'as si_type,'抽检'as si_type_name,'20'as si_degree,'正常'as si_degree_name,'10'as si_level,'一般'as si_level_name,''as si_aql,0,0,
		null,null,false,null,null,
		null,null,null,null,null,null,
		null,null,null,null,null,null,
		null,_ea_no,_ea_name,
		now(),_user_id,_user_no,_user_name,'pda',
		now(),_user_id,_user_no,_user_name,'pda',
		false,0,null
	from temp_table_scwgsj a
	left join wm_sn ws on ws.sn_no = a.sn_no
	left join mo m on ws.mo_no = m.mo_no 
	left join ss_factory sf on sf.factory_no = m.factory_no 
	group by _si_lot_h_id,_si_lot_h_no,m.factory_no,sf.factory_name,ws.part_no,ws.part_name,
		ws.part_spec,ws.wkp_no ,ws.wkp_name ,
		_me_finish_io_no,_me_finish_io_id_h,m.order_no1 ,m.client_no,
		m.client_name ;
	
	--写入送检单条码明细
	INSERT INTO public.qm_si_lot_b_sn
	(si_lot_b_sn_id, si_lot_h_id, si_lot_b_id, sn_no, si_conclusion, qa_cause_no, qa_cause_name, 
	si_lot_b_sn_rmk01, si_lot_b_sn_rmk02, si_lot_b_sn_rmk03, si_lot_b_sn_rmk04, si_lot_b_sn_rmk05, 
	si_lot_b_sn_rmk06, crt_time, crt_user, crt_user_no, crt_user_name, crt_host, 
	upd_time, upd_user, upd_user_no, upd_user_name, upd_host, 
	sn_pack_20, sn_pack_30, sn_pack_40, sn_pack_50, part_qty, 
	mo_no, weight_gross, weight_net, pack_qty_used, ea_no, ea_name,
	sn_type,sn_type_name)
	select 
	af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
	null,null,null,null,null,
	null,now(),_user_id,_user_no,_user_name,'pda',
	now(),_user_id,_user_no,_user_name,'pda',
	ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
	ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
	ws.sn_type,ws.sn_type_name
	from temp_table_scwgsj a
	left join wm_sn ws on a.sn_no = ws.sn_no 	
	union all 
	select 
	af_auid(), _si_lot_h_id,af_auid(),ws.sn_no ,null,null,null,
	null,null,null,null,null,
	null,now(),_user_id,_user_no,_user_name,'pda',
	now(),_user_id,_user_no,_user_name,'pda',
	ws.sn_pack_20 ,ws.sn_pack_30,ws.sn_pack_40,ws.sn_pack_50,ws.part_qty ,
	ws.mo_no ,ws.weight_gross ,ws.weight_net ,ws.pack_qty_used ,ws.ea_no ,ws.ea_name ,
	ws.sn_type,ws.sn_type_name
	from temp_table_scwgsj a
	left join wm_sn ws on a.sn_no = ws.sn_pack_50  	
	;
	
	
 end if;   

*/
 	
	  
	results := row('true','成功!');      
    return to_json(results);   
end;  
 $function$
;

```



逻辑分析：




## PDA MES生产完工入库(送检)功能分析

基于 <mcfile name="PDA_MES生产完工入库(送检).md" path="d:\金洋\成品出入库记录\入库\PDA_MES生产完工入库(送检).md"></mcfile> 文件中的PostgreSQL函数，我来分析这个功能的代码逻辑、业务逻辑和数据逻辑：

### 功能概述
函数 `af_jiaoyantiaoma_finish_io` 是生产完工入库送检的核心校验函数，主要用于扫描外箱条码进行完工送检前的各项校验。

### 代码逻辑分析

#### 1. 输入参数处理
```sql
_datas := json(datas);
_sn_no := _datas->'datas'->0->>'sn_no';
_ea_no := _datas->'datas'->0->>'ea_no';
```

#### 2. 校验流程（按执行顺序）
1. **基础参数校验**：外箱条码不能为空
2. **条码存在性校验**：验证外箱条码是否存在
3. **条码类型校验**：必须是外箱类型(sn_type='40')
4. **条码状态校验**：必须是确认状态(sn_status='110')
5. **包装完成校验**：验证外箱是否包装完成
6. **产线一致性校验**：验证工单产线与入库产线是否一致
7. **重复送检校验**：防止重复送检扫描
8. **过程检验校验**：检查是否存在不合格的过程检验

#### 3. 返回结果处理
```sql
_results := row('true','扫码成功','[{"sn_no":"'||_sn_no||'"}]');
return to_json(_results);
```

### 业务逻辑分析

#### 1. 外箱条码管理
- **条码类型控制**：只允许外箱类型条码(sn_type='40')进行送检
- **状态管理**：条码必须处于确认状态才能送检
- **包装完成验证**：确保外箱已完成包装(`pack_qty_used = part_qty`)

#### 2. 产线管控逻辑
```sql
-- 江西产线特殊处理
if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%') then
    -- 验证工单与产线的关联关系
else
    -- 常规产线验证
end if;
```

#### 3. 重工单特殊处理
```sql
_mo_no_f := substring(_mo_no,1,1);
if(_mo_no_f = 'F') then  -- 重工单以F开头
    -- 重工单允许同一条码在不同工单下重新送检
else
    -- 普通工单不允许重复送检
end if;
```

#### 4. 质量管控
- **过程检验校验**：检查OQC过程检验是否判定为不合格
- **批退处理**：对于批退的不合格品，逐一检查外箱内的产品条码

### 数据逻辑分析

#### 1. 核心数据表
- **wm_sn**：条码主表
  - `sn_no`：条码号
  - `sn_type`：条码类型('40'=外箱)
  - `sn_status`：条码状态('110'=确认)
  - `pack_qty_used`：已包装数量
  - `part_qty`：计划包装数量
  - `sn_pack_50`：外箱绑定关系

- **mo**：生产订单表
  - `mo_no`：工单号
  - `workshop_no`：车间编号
  - `mo_parent_top`：顶层工单
  - `part_no`：物料编号

- **me_finish_io/me_finish_io_h**：完工入库表
  - `me_finish_io_no`：完工入库单号
  - `finish_io_status`：入库状态
  - `sn_no`：条码号

- **qm_si_lot_h/qm_si_lot_b_sn**：检验批次表
  - `si_lot_move_type`：检验类型('303'=过程检验)
  - `si_conclusion_name`：检验结论

#### 2. 数据验证逻辑
```sql
-- 物料编码一致性验证
if coalesce(_part_no_sn,'') <> coalesce(_part_no_mo,'') then
    -- 标签物料编码与工单物料编码不一致
end if;
```

#### 3. 关联关系验证
- **条码与工单关联**：通过wm_sn表关联
- **工单与产线关联**：通过mo_rmb_ea表关联
- **外箱与产品条码关联**：通过sn_pack_50字段关联

### 关键业务规则

#### 1. 条码管控规则
- 外箱条码必须是确认状态且包装完成
- 不允许重复送检（重工单除外）
- 条码类型必须匹配业务场景

#### 2. 产线管控规则
- 江西产线采用特殊的关联验证逻辑
- 工单产线与入库产线必须一致
- 委外分厂产线需要额外校验

#### 3. 质量管控规则
- 过程检验不合格的产品不能送检
- 批退产品需要逐一验证外箱内的产品条码
- 重工单允许重新送检

#### 4. 异常处理
- 每个校验环节都有明确的错误提示
- 使用统一的返回格式
- 提供详细的错误信息便于问题定位

### 总结
这个函数实现了生产完工送检的全面校验，涵盖了条码管理、产线管控、质量管控等多个维度，体现了严格的生产质量管理要求。通过多层次的校验确保只有符合条件的产品才能进入送检流程，有效防止了质量风险和操作错误。
        


好的，这是一个非常关键的问题。

经过详细分析，这个存储过程 `af_jiaoyantiaoma_finish_io` **本身并不生成** `me_finish_io_h` 和 `me_finish_io` 表的数据。

它的核心作用是一个**前置校验网关 (Validation Gateway)**。您可以把它理解为一道安检门，在正式创建"完工入库送检单"之前，对操作员扫描的每一个外箱条码进行严格的资格审查。

只有当所有检查都通过时，它才会返回 `'true'`，这相当于给PDA前端一个信号："这个条码是合格的，你可以将它加入到本次待送检的清单中。"

---

### 推断的完整业务与数据生成流程

既然这个函数只做校验，那么 `me_finish_io_h` 和 `me_finish_io` 这两张表的数据是在何时、由何程序生成的呢？结合业务场景，完整的流程应该是这样的：

```mermaid
sequenceDiagram
    participant PDA as PDA/前端应用
    participant af_jiaoyantiaoma_finish_io as 校验函数 (本文件)
    participant GenerateFunction as [推断] 后端生成函数
    participant DB as 数据库

    loop 每次扫描外箱
        PDA->>af_jiaoyantiaoma_finish_io: 1. 发送sn_no进行校验
        af_jiaoyantiaoma_finish_io-->>PDA: 2. 返回校验结果 ('true'或'false')
        alt 校验成功
            PDA->>PDA: 3. 将合格的sn_no添加到本地列表
        else 校验失败
            PDA->>PDA: 4. 显示错误信息
        end
    end
    
    Note over PDA: 操作员完成所有扫描后，点击"提交"按钮

    PDA->>GenerateFunction: 5. 将整个sn_no列表提交
    
    GenerateFunction->>DB: 6. **INSERT INTO me_finish_io_h** (创建头信息)
    DB-->>GenerateFunction: 7. 返回 me_finish_io_h_id
    
    loop 遍历sn_no列表
        GenerateFunction->>DB: 8. **INSERT INTO me_finish_io** (创建明细)
    end
    
    GenerateFunction-->>PDA: 9. 返回最终成功或失败信息
```

### 数据生成逻辑详解

根据上述流程，`me_finish_io_h` 和 `me_finish_io` 是在用户点击"提交"后，由另一个我们目前未见的后端函数（我称之为 `GenerateFunction`）生成的。其生成逻辑如下：

#### 1. `me_finish_io_h` (头表) 的生成
当 `GenerateFunction` 接收到PDA提交的序列号列表后，它会执行**一次** `INSERT` 操作来创建头记录：
-   **`me_finish_io_no`**: 调用 `af_ss_no_generate()` 函数生成一个新的、唯一的"完工入库单号"。
-   **`finish_io_status`**: 设置一个初始状态，很可能是"待检验"或类似的状态。
-   **`crt_user`, `crt_time` 等**: 记录当前操作员和操作时间。
-   **`me_finish_io_h_id`**: 通过 `af_auid()` 生成一个唯一的主键。
-   其他字段如 `workshop_no` 等，会从第一个 `sn_no` 关联的工单中获取，因为同一张送检单通常属于同一个车间。

#### 2. `me_finish_io` (明细表) 的生成
在创建了头表记录后，`GenerateFunction` 会**遍历** PDA提交过来的每一个 `sn_no`，并为每一个 `sn_no` 执行一次 `INSERT` 操作来创建明细记录：
-   **`me_finish_io_h_id`**: 写入上一步中生成的头表主键，将明细与头关联起来。
-   **`sn_no`**: 写入当前遍历到的外箱序列号。
-   **`mo_no`, `part_no`, `part_qty` 等**: 这些信息会通过 `SELECT ... FROM wm_sn WHERE sn_no = [当前sn_no]` 从序列号主数据表 `wm_sn` 中查询并带入。
-   **`me_finish_io_rmk4`**: 初始状态可能为空，等待后续的"暂收"或"入库"操作来更新。

### 总结

-   **`af_jiaoyantiaoma_finish_io` 的角色**: **守门员**。它负责审查每个SN的资格，但不创建任何最终业务单据。
-   **数据生成的时机**: 在所有条码扫描和校验完成，用户在PDA上点击**"提交"**之后。
-   **数据生成的执行者**: 一个未在当前文件中体现的、负责处理最终提交的**后端函数**。这个函数负责整合所有已校验的SN，并批量生成 `me_finish_io_h` 和 `me_finish_io` 的数据。