
```sql
select ma.me_finish_io_no,
case when strpos(ma.mo_no,'F')=0 then
json_build_object('DocDate',to_char(ma.finish_io_datetime,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
json_agg(json_build_object('BaseType',202,'BaseRef',ma.mo_no,'WhsCode',mc.invp_area_no,'QuanTity',finish_io_qty_ok,'BatchNo',lot_no,'AcctCode',mb.acct_code,'U_BaseType','FQCTEMP','U_QtDocNum',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_QtLine',1)))
else json_build_object('DocDate',to_char(ma.finish_io_datetime,'yyyy-mm-dd'),'U_MES','Y','U_WebNo',ma.me_finish_io_no,'details',
json_agg(json_build_object('ItemCode',ma.part_no,'WhsCode',mc.invp_area_no,'QuanTity',ma.finish_io_qty_ok,'BatchNo',ma.lot_no,'AcctCode',mb.acct_code,'Price',mb.price,'U_BaseType','SPLIT1','U_OPLANNUM',substring(ma.sap_inbound_apply_no,strpos(ma.sap_inbound_apply_no,'：')+1),'U_Reqline','1','U_QtDocNum',replace(ma.mo_no,'F',''),'U_QtLine',1))) end  as datas
from me_finish_io_h ma
left join mo mb on mb.mo_no=ma.mo_no
left join pd_part mc on mc.part_no=ma.part_no
where ma.me_finish_io_rmk4='入库完成' and coalesce(ma.sap_inbound_apply_no,'')!='' and coalesce(ma.sap_inbound_no,'')=''
group by ma.me_finish_io_no

```

```javascript
function main() {
	var success=[];
	var fail=[];
	
var Prev=Request.Prev;
Log.LogInfo(JSON.stringify(Prev));

for(var i=0;i<Prev.length;i++) {
	var req_params=Prev[i];
	Log.LogInfo(req_params.me_finish_io_no);

	var params={
		"method": "mes-sap-OIGN",
		"data": JSON.parse(req_params.datas)
	};
	Log.LogInfo(JSON.stringify(params));

	var response = HttpApi.post(
		ss_io_tbl.io_write_auth.io_auth_db_host,
		{body:params}
	);

	var result=JSON.parse(response.Content.ReadAsStringAsync().Result);
	Log.LogInfo(JSON.stringify(result));

	if(result.code!=0){
		fail.push(result.message)
		throw result.message;
	}else{
		success.push(JSON.stringify(result));
		ReadContext.ExecuteSql("update me_finish_io_h set sap_inbound_no='"+result.message+"',upd_time=localtimestamp where me_finish_io_no='"+finish_io_no+"'");
	}

}

return {"success":success,"fail":fail};

}

```