-- public.pd_part definition

-- Drop table

-- DROP TABLE public.pd_part;

CREATE TABLE public.pd_part (
	part_id text DEFAULT af_auid() NOT NULL,
	part_status text DEFAULT '110'::text NOT NULL,
	part_no text NOT NULL,
	part_name text NOT NULL,
	part_spec text NULL,
	part_unit text DEFAULT 'PCS'::text NOT NULL,
	part_type_product text DEFAULT 'O'::text NOT NULL,
	part_type01 text NULL,
	part_type02 text NULL,
	part_type03 text NULL,
	part_type04 text NULL,
	part_type05 text NULL,
	part_type06 text NULL,
	part_type07 text NULL,
	part_type08 text NULL,
	part_type09 text NULL,
	part_type10 text NULL,
	part_type11 text NULL,
	part_type12 text NULL,
	part_type13 text NULL,
	part_type14 text NULL,
	part_type15 text NULL,
	part_type16 text NULL,
	part_type17 text NULL,
	part_type18 text NULL,
	part_type19 text NULL,
	part_type20 text NULL,
	part_spec01 text NULL,
	part_spec02 text NULL,
	part_spec03 text NULL,
	part_spec04 text NULL,
	part_spec05 text NULL,
	part_spec06 text NULL,
	part_spec07 text NULL,
	part_spec08 text NULL,
	part_spec09 text NULL,
	part_spec10 text NULL,
	part_spec11 text NULL,
	part_spec12 text NULL,
	part_spec13 text NULL,
	part_spec14 text NULL,
	part_spec15 text NULL,
	part_spec16 text NULL,
	part_spec17 text NULL,
	part_spec18 text NULL,
	part_spec19 text NULL,
	part_spec20 text NULL,
	part_spec21 text NULL,
	part_spec22 text NULL,
	part_spec23 text NULL,
	part_spec24 text NULL,
	part_spec25 text NULL,
	part_spec26 text NULL,
	part_spec27 text NULL,
	part_spec28 text NULL,
	part_spec29 text NULL,
	part_spec30 text NULL,
	doc_keyword text NULL,
	part_color text NULL,
	part_length int4 DEFAULT 0 NOT NULL,
	part_width int4 DEFAULT 0 NOT NULL,
	part_height int4 DEFAULT 0 NOT NULL,
	part_weight_net numeric(18, 6) DEFAULT 0 NOT NULL,
	part_weight_gross numeric(18, 6) DEFAULT 0 NOT NULL,
	part_weight_leftover numeric(18, 6) DEFAULT 0 NOT NULL,
	safe_storage_qty_max numeric(18, 3) DEFAULT 99999999 NOT NULL,
	safe_storage_qty_warn numeric(18, 3) DEFAULT 99999999 NOT NULL,
	safe_storage_qty_min numeric(18, 3) DEFAULT 0 NOT NULL,
	sale_qty_min numeric(18, 3) DEFAULT 1 NOT NULL,
	sale_qty_add numeric(18, 3) DEFAULT 1 NOT NULL,
	lead_time_produce_start int4 DEFAULT 0 NOT NULL,
	lead_time_produce_end int4 DEFAULT 0 NOT NULL,
	eag_id text NULL,
	part_uph numeric(18, 3) DEFAULT 1 NOT NULL,
	mo_ok_rate numeric(18, 3) DEFAULT 100 NOT NULL,
	mo_qty_min numeric(18, 3) DEFAULT 1 NOT NULL,
	mo_qty_max numeric(18, 3) DEFAULT 9999999 NOT NULL,
	mo_qty_add numeric(18, 3) DEFAULT 1 NOT NULL,
	mo_qty_lot numeric(18, 3) DEFAULT 1 NOT NULL,
	part_difficulty_rate numeric(18, 3) DEFAULT 1 NOT NULL,
	load_material_lead_time int4 DEFAULT 0 NOT NULL,
	load_material_span_time int4 DEFAULT 0 NOT NULL,
	load_material_speed numeric(18, 3) DEFAULT 0 NOT NULL,
	is_manipulator bool DEFAULT false NOT NULL,
	is_special bool DEFAULT false NOT NULL,
	lead_time_purchase int4 DEFAULT 20 NOT NULL,
	unit_price_standard numeric(18, 3) DEFAULT 0 NOT NULL,
	unit_price_average numeric(18, 3) DEFAULT 0 NOT NULL,
	unit_price_manual numeric(18, 3) DEFAULT 0 NOT NULL,
	cost_punish_delay0 numeric(18, 3) DEFAULT 1 NOT NULL,
	cost_punish_delay1 numeric(18, 3) DEFAULT 1 NOT NULL,
	mrp_region_no text NULL,
	inv_io_nocheck bool NULL,
	rmh_no text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	valid_day int4 DEFAULT 0 NOT NULL,
	is_sn_manage bool DEFAULT false NOT NULL,
	si_degree text NULL,
	si_level text NULL,
	si_aql text NULL,
	bom_layer int4 DEFAULT 0 NOT NULL,
	bom_wkt_before int4 DEFAULT 0 NOT NULL,
	bom_layer_next int4 DEFAULT 0 NOT NULL,
	is_main_mtr bool DEFAULT false NOT NULL,
	plan_merge_rule int4 DEFAULT 0 NOT NULL,
	plan_throw_days int4 DEFAULT 0 NOT NULL,
	plan_merge_min_days int4 DEFAULT 0 NOT NULL,
	plan_merge_min_qty int4 DEFAULT 0 NOT NULL,
	plan_merge_max_days int4 DEFAULT 0 NOT NULL,
	punish_delay0_days int4 DEFAULT 0 NOT NULL,
	punish_ahead0_days int4 DEFAULT 0 NOT NULL,
	cost_punish_ahead0 int4 DEFAULT 0 NOT NULL,
	cost_punish_ahead1 int4 DEFAULT 0 NOT NULL,
	plan_merge_max_qty int4 DEFAULT 0 NOT NULL,
	is_continue_purchase bool DEFAULT true NOT NULL,
	rework_mo_ok_rate numeric(18, 3) DEFAULT 100 NOT NULL,
	lead_time_produce int4 DEFAULT 1 NOT NULL,
	is_fifo bool DEFAULT false NOT NULL,
	is_valid_date_manage bool DEFAULT false NOT NULL,
	data_precision int4 DEFAULT 0 NULL,
	is_bd_tm bool DEFAULT false NOT NULL,
	is_no_ct_scan bool DEFAULT false NULL,
	insertion_loss_test_code text NULL,
	is_ct_scan_ok bool DEFAULT false NULL,
	item_description text NULL,
	mpn text NULL,
	invp_area_no text NULL,
	fujianeirong text NULL,
	mibiao text NULL,
	item_description_en text NULL,
	duiwaixinghao text NULL,
	remark text NULL,
	is_only_part bool NULL,
	batch_no_flag text NULL,
	CONSTRAINT pk_pd_part PRIMARY KEY (part_id)
)
WITH (
	autovacuum_enabled=true
);
CREATE UNIQUE INDEX ix_pd_part ON public.pd_part USING btree (part_no);
CREATE INDEX pd_part_is_bd_tm_idx ON public.pd_part USING btree (is_bd_tm);
CREATE INDEX pd_part_is_no_ct_scan_idx ON public.pd_part USING btree (is_no_ct_scan);
CREATE INDEX pd_part_part_no_idx ON public.pd_part USING btree (part_no, is_no_ct_scan);
CREATE INDEX pd_part_part_unit_idx ON public.pd_part USING btree (part_unit);


-- public.ea definition

-- Drop table

-- DROP TABLE public.ea;

CREATE TABLE public.ea (
	ea_id text DEFAULT af_auid() NOT NULL,
	ea_status text DEFAULT '110'::text NOT NULL,
	ea_no text NOT NULL,
	ea_name text NOT NULL,
	ea_card_no text NULL,
	ea_type1 text NULL,
	ea_type2 text NULL,
	ea_type3 text NULL,
	ea_type4 text NULL,
	ea_type5 text NULL,
	ea_type6 text NULL,
	ea_type7 text NULL,
	ea_type8 text NULL,
	ea_worktime_type text DEFAULT 'A'::text NOT NULL,
	ea_eff_standard numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_eff_now numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_capability_min numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_capability_max numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_capability_step numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_wkt_step numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_wkt_max_waiting numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_wkt_outsource numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_worker_num numeric(12, 2) DEFAULT 1 NOT NULL,
	ea_spec1_txt text NULL,
	ea_spec1_min numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec1_max numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec2_txt text NULL,
	ea_spec2_min numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec2_max numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec3_txt text NULL,
	ea_spec3_min numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec3_max numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec4_txt text NULL,
	ea_spec4_min numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_spec4_max numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_cost_before_c numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_cost_after_c numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_cost_idle_c numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_cost_produce_c numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_cost_worker_c numeric(12, 2) DEFAULT 0 NOT NULL,
	ea_sort text DEFAULT '0' NOT NULL,
	ea_color text NULL,
	ea_height int4 DEFAULT 0 NOT NULL,
	factory_id text NOT NULL,
	dept_no text NULL,
	line_name text NULL,
	invp_id text NULL,
	to_invp_id text NULL,
	ea_rmk01 text NULL,
	ea_rmk02 text NULL,
	ea_rmk03 text NULL,
	ea_rmk04 text NULL,
	ea_rmk05 text NULL,
	ea_rmk06 text NULL,
	ea_rmk07 text DEFAULT NULL::timestamp without time zone NULL,
	ea_rmk08 text NULL,
	ea_rmk09 text NULL,
	ea_rmk10 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp(0) NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	key_job_id text NULL,
	key_job_constrain_aps bool DEFAULT false NOT NULL,
	coordinate_x int4 DEFAULT 0 NOT NULL,
	coordinate_y int4 DEFAULT 0 NOT NULL,
	before_key_job_constrain_aps bool DEFAULT false NULL,
	before_key_job_id text NULL,
	jy_guige text NULL,
	invp_no text NULL,
	is_line bool DEFAULT false NOT NULL,
	dept_name text NULL,
	CONSTRAINT ix_ea UNIQUE (factory_id, ea_no),
	CONSTRAINT pk_ea PRIMARY KEY (ea_id),
	CONSTRAINT ea_fk_factory_id FOREIGN KEY (factory_id) REFERENCES public.ss_factory(factory_id) ON DELETE RESTRICT ON UPDATE CASCADE
)
WITH (
	autovacuum_enabled=true
);
CREATE INDEX ea_ea_no_idx ON public.ea USING btree (ea_no);
CREATE INDEX ea_invp_id_idx ON public.ea USING btree (invp_id);


-- public.ss_user definition

-- Drop table

-- DROP TABLE public.ss_user;

CREATE TABLE public.ss_user (
	user_id text DEFAULT af_auid() NOT NULL,
	zstatus text NOT NULL,
	user_no text NOT NULL,
	user_pwd text NOT NULL,
	user_name text NOT NULL,
	user_title text NULL,
	user_sex text NULL,
	allow_logon bool DEFAULT true NULL,
	cannot_change_password bool DEFAULT false NULL,
	valid_begin_time timestamp NULL,
	valid_end_time timestamp NULL,
	login_begin_time int4 NULL,
	login_end_time int4 NULL,
	user_mobile text NULL,
	user_tel text NULL,
	user_email text NULL,
	rmk1 text NULL,
	rmk2 text NULL,
	rmk3 text NULL,
	company_id_default text NULL,
	factory_id_default text NULL,
	dept_id_default text NULL,
	workshop_id_default text NULL,
	line_id_default text NULL,
	worker_id_default text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	user_load_lang bool DEFAULT true NULL,
	user_token text NULL,
	worker_id text NULL,
	unlock_weight_token text NULL,
	CONSTRAINT pk_ss_user PRIMARY KEY (user_id)
)
WITH (
	autovacuum_enabled=true
);
CREATE UNIQUE INDEX ix_user_no_unique ON public.ss_user USING btree (user_no);

-- Table Triggers

create trigger ss_user_trigger after
insert
    or
update
    on
    public.ss_user for each row execute function af_ss_user_trigger();



  -- public.cr_client definition

-- Drop table

-- DROP TABLE public.cr_client;

CREATE TABLE public.cr_client (
	client_id text NOT NULL,
	client_status text DEFAULT '110'::text NOT NULL,
	client_no text DEFAULT af_ss_no_generate('client_no'::character varying) NOT NULL,
	client_name text NOT NULL,
	client_name_py text NULL,
	client_name_abbr text NOT NULL,
	client_name_abbr_py text NULL,
	client_country text NULL,
	client_province text NULL,
	client_city text NULL,
	client_address text NULL,
	client_postcode text NULL,
	client_tel text NULL,
	client_fax text NULL,
	client_email text NULL,
	client_website text NULL,
	client_priority int4 DEFAULT 1 NULL,
	client_owner_user_id text NULL,
	client_payment text NULL,
	client_settle text NULL,
	client_trad_currency text NULL,
	client_discount numeric(18, 4) NULL,
	client_trans_days numeric(18, 4) DEFAULT 1 NULL,
	client_owner_last text NULL,
	mrp_region_no text NULL,
	mrp_type text DEFAULT 'O'::character varying NULL,
	doc_keyword text NULL,
	client_type1 text NULL,
	client_type2 text NULL,
	client_type3 text NULL,
	client_type4 text NULL,
	client_type5 text DEFAULT '1' NULL,
	client_type6 text NULL,
	client_type7 text NULL,
	client_type8 text NULL,
	client_type9 text NULL,
	client_type10 text NULL,
	client_rmk01 text NULL,
	client_rmk02 text NULL,
	client_rmk03 text NULL,
	client_rmk04 text NULL,
	client_rmk05 text NULL,
	client_rmk06 text NULL,
	client_rmk07 text NULL,
	client_rmk08 text NULL,
	client_rmk09 text NULL,
	client_rmk10 text NULL,
	client_rmk11 text NULL,
	client_rmk12 text NULL,
	client_rmk13 text NULL,
	client_rmk14 text NULL,
	client_rmk15 text NULL,
	client_rmk16 text NULL,
	client_rmk17 text NULL,
	client_rmk18 text NULL,
	client_rmk19 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	CONSTRAINT pk_cr_client PRIMARY KEY (client_id),
	CONSTRAINT unique_client_no UNIQUE (client_no)
)
WITH (
	autovacuum_enabled=true
);




-- public.mo definition

-- Drop table

-- DROP TABLE public.mo;

CREATE TABLE public.mo (
	mo_id text DEFAULT af_auid() NOT NULL,
	mo_no text DEFAULT af_ss_no_generate('mo_no'::character varying) NOT NULL,
	mo_status text DEFAULT '110'::text NOT NULL,
	part_no text NOT NULL,
	part_name text NULL,
	part_spec text NULL,
	part_idt text NULL,
	mrp_region_no text NULL,
	rmh_no text NULL,
	mo_qty numeric(18, 3) DEFAULT 1 NULL,
	mo_hope_start_datetime date DEFAULT now() NOT NULL,
	mo_hope_end_datetime date DEFAULT now() NOT NULL,
	mo_lot_no text NULL,
	mo_desc text NULL,
	factory_no text DEFAULT '1' NULL,
	workshop_no text NULL,
	mo_type text DEFAULT '20'::text NULL,
	mo_priority int4 DEFAULT 500 NULL,
	mo_priority2 int4 DEFAULT 500 NULL,
	mo_need_aps bool DEFAULT true NULL,
	mo_cannot_earlier bool DEFAULT false NULL,
	mo_cannot_earlier_than timestamp DEFAULT now() NULL,
	client_no text NULL,
	client_name text NULL,
	mo_parent text NULL,
	mo_parent_top text NULL,
	order_id1 text NULL,
	order_type1 text NULL,
	order_no1 text NULL,
	order_type_name1 text NULL,
	order_mrp_region_no1 text NULL,
	order_id2 text NULL,
	order_type2 text NULL,
	order_no2 text NULL,
	order_type_name2 text NULL,
	order_mrp_region_no2 text NULL,
	mo_rmk01 text NULL,
	mo_rmk02 text NULL,
	mo_rmk03 text NULL,
	mo_rmk04 text NULL,
	mo_rmk05 text NULL,
	mo_rmk06 text NULL,
	mo_rmk07 text NULL,
	mo_rmk08 text NULL,
	mo_rmk09 text NULL,
	mo_rmk10 text NULL,
	mo_rmk11 text NULL,
	mo_rmk12 text NULL,
	mo_rmk13 text NULL,
	mo_rmk14 text NULL,
	mo_rmk15 text NULL,
	mo_rmk16 text NULL,
	mo_rmk17 text NULL,
	mo_rmk18 text NULL,
	mo_rmk19 text NULL,
	mo_rmk20 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	mo_qty_is_input bool DEFAULT false NULL,
	mo_stock_in_qty_total numeric(18, 3) DEFAULT 0 NULL,
	rmh_version text NULL,
	mcpn text NULL,
	emcpn text NULL,
	client_version text NULL,
	inner_version text NULL,
	client_parent_version text NULL,
	ea_issue_ok bool DEFAULT false NULL,
	client_po text NULL,
	acct_code text NULL,
	price numeric DEFAULT 0 NOT NULL,
	CONSTRAINT pk_mo PRIMARY KEY (mo_id),
	CONSTRAINT unique_mo_no UNIQUE (mo_no),
	CONSTRAINT mo_fk_factory_no FOREIGN KEY (factory_no) REFERENCES public.ss_factory(factory_no) ON DELETE RESTRICT ON UPDATE CASCADE
)
WITH (
	autovacuum_enabled=true
);
CREATE UNIQUE INDEX ix_mo ON public.mo USING btree (mo_no, factory_no);
CREATE INDEX ix_mo_1 ON public.mo USING btree (mo_status);
CREATE INDEX ix_mo_3 ON public.mo USING btree (factory_no);
CREATE INDEX ix_mo_4 ON public.mo USING btree (mo_hope_end_datetime);
CREATE INDEX ix_mo_5 ON public.mo USING btree (mo_lot_no);
CREATE INDEX mo_mo_no_idx ON public.mo USING btree (mo_no, mo_rmk01);
CREATE INDEX mo_part_no_idx ON public.mo USING btree (part_no);
CREATE INDEX mo_rmh_no_idx ON public.mo USING btree (rmh_no);




-- public.wm_sn definition

-- Drop table

-- DROP TABLE public.wm_sn;

CREATE TABLE public.wm_sn (
	sn_no text DEFAULT af_ss_no_generate('sn_no'::character varying) NOT NULL,
	sn_type text NULL,
	sn_type_name text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_idt text NULL,
	part_qty numeric DEFAULT 0 NULL,
	lot_no text NULL,
	supplier_id text NULL,
	supplier_no text NULL,
	supplier_name text NULL,
	valid_date_to date NULL,
	sn_status text NULL,
	sn_status_name text NULL,
	qa_type text NULL,
	mo_no text NULL,
	unode text NULL,
	wkn text NULL,
	wkp_no text NULL,
	wkp_name text NULL,
	ea_id text NULL,
	ea_no text NULL,
	ea_name text NULL,
	invp_id text NULL,
	invp_no text NULL,
	sn_pack_20 text NULL,
	sn_pack_30 text NULL,
	sn_pack_40 text NULL,
	sn_pack_50 text NULL,
	factory_no text NULL,
	pack_rule text NULL,
	pack_rule_name text NULL,
	pack_qty_max numeric(12, 3) NULL,
	pack_qty_used numeric(12, 3) NULL,
	pack_is_closed bool DEFAULT false NOT NULL,
	print_times int4 DEFAULT 0 NOT NULL,
	weight_gross numeric(12, 3) DEFAULT 0 NULL,
	weight_net numeric(12, 2) DEFAULT 0 NULL,
	length numeric(12, 3) DEFAULT 0 NOT NULL,
	width numeric(12, 2) DEFAULT 0 NOT NULL,
	height numeric(12, 2) DEFAULT 0 NOT NULL,
	fb_id text NULL,
	sr_dlv_h_id text NULL,
	wm_sn_rmk01 text NULL,
	wm_sn_rmk02 text NULL,
	wm_sn_rmk03 text NULL,
	wm_sn_rmk04 text NULL,
	wm_sn_rmk05 text NULL,
	wm_sn_rmk06 text NULL,
	crt_time timestamp NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	sn_orgin_type text NULL,
	sn_orgin_type_name text NULL,
	is_qa_ng bool DEFAULT false NOT NULL,
	is_hold_to_use bool DEFAULT false NOT NULL,
	po_h_no text NULL,
	produce_date timestamp NULL,
	is_lock bool DEFAULT false NOT NULL,
	seq_no int4 DEFAULT 0 NULL,
	ct_scan_ok bool DEFAULT false NULL,
	send_out_date timestamp NULL,
	year_week text NULL,
	client_po text NULL,
	emcpn text NULL,
	client_version text NULL,
	fujianeirong text NULL,
	mibiao text NULL,
	mcpn text NULL,
	lianjieqileixing text NULL,
	lianjieqileixing1 text NULL,
	charusunhao text NULL,
	charusunhao1 text NULL,
	huibosunhao text NULL,
	huibosunhao1 text NULL,
	changdu text NULL,
	guangxianleixing text NULL,
	changjiaguige text NULL,
	beizhu text NULL,
	huaweidaima text NULL,
	client_parent_version text NULL,
	invp_area_no text NULL,
	inventory_lot text NULL,
	sign_name text NULL,
	sn_sign_code text DEFAULT 'M'::text NOT NULL,
	CONSTRAINT wm_sn_pkey PRIMARY KEY (sn_no)
)
PARTITION BY HASH (sn_no);
CREATE INDEX ix_wn_sn_fb_id ON ONLY public.wm_sn USING hash (fb_id);
CREATE INDEX ix_wn_sn_part_no ON ONLY public.wm_sn USING hash (part_no);
CREATE INDEX ix_wn_sn_sn_pack_20 ON ONLY public.wm_sn USING hash (sn_pack_20);
CREATE INDEX ix_wn_sn_sn_pack_30 ON ONLY public.wm_sn USING hash (sn_pack_30);
CREATE INDEX ix_wn_sn_sn_pack_40 ON ONLY public.wm_sn USING hash (sn_pack_40);
CREATE INDEX ix_wn_sn_sn_pack_50 ON ONLY public.wm_sn USING hash (sn_pack_50);
CREATE INDEX ix_wn_sn_sr_dlv_h_id ON ONLY public.wm_sn USING hash (sr_dlv_h_id);
CREATE INDEX wm_sn_ea_no_idx ON ONLY public.wm_sn USING btree (ea_no);
CREATE INDEX wm_sn_invp_no_idx ON ONLY public.wm_sn USING btree (invp_no);
CREATE INDEX wm_sn_is_hold_to_use_idx ON ONLY public.wm_sn USING btree (is_hold_to_use);
CREATE INDEX wm_sn_is_qa_ng_idx ON ONLY public.wm_sn USING btree (is_qa_ng);
CREATE INDEX wm_sn_lot_no_idx ON ONLY public.wm_sn USING btree (lot_no);
CREATE INDEX wm_sn_mo_no_idx ON ONLY public.wm_sn USING btree (mo_no);
CREATE INDEX wm_sn_po_h_no_idx ON ONLY public.wm_sn USING btree (po_h_no);
CREATE INDEX wm_sn_produce_date_idx ON ONLY public.wm_sn USING btree (produce_date);
CREATE INDEX wm_sn_sn_no_idx2 ON ONLY public.wm_sn USING btree (sn_no);
CREATE INDEX wm_sn_sn_no_idx3 ON ONLY public.wm_sn USING btree (sn_no varchar_pattern_ops);
CREATE INDEX wm_sn_sn_no_idx5 ON ONLY public.wm_sn USING btree (sn_no, mo_no, sn_type);
CREATE INDEX wm_sn_sn_no_idx6 ON ONLY public.wm_sn USING btree (sn_no, sn_status, mo_no, sn_type);
CREATE INDEX wm_sn_sn_status_idx ON ONLY public.wm_sn USING btree (sn_status);
CREATE INDEX wm_sn_sn_status_name_idx ON ONLY public.wm_sn USING btree (sn_status_name);
CREATE INDEX wm_sn_sn_type_idx ON ONLY public.wm_sn USING btree (sn_type);
CREATE INDEX wm_sn_sn_type_name_idx ON ONLY public.wm_sn USING btree (sn_type_name);
CREATE INDEX wm_sn_supplier_name_idx ON ONLY public.wm_sn USING btree (supplier_name);
CREATE INDEX wm_sn_supplier_no_idx ON ONLY public.wm_sn USING btree (supplier_no);
CREATE INDEX wm_sn_valid_date_to_idx ON ONLY public.wm_sn USING btree (valid_date_to);
CREATE INDEX wm_sn_wm_sn_rmk02_idx ON ONLY public.wm_sn USING btree (wm_sn_rmk02);
CREATE INDEX wm_sn_wm_sn_rmk04_idx ON ONLY public.wm_sn USING btree (wm_sn_rmk04);
CREATE INDEX wm_sn_year_week_idx ON ONLY public.wm_sn USING btree (year_week);


-- public.szjy_wm_inventory definition

-- Drop table

-- DROP TABLE public.szjy_wm_inventory;

CREATE TABLE public.szjy_wm_inventory (
	inventory_id text DEFAULT af_auid() NOT NULL,
	part_no text NOT NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_qty numeric DEFAULT 0 NOT NULL,
	lot_no text NOT NULL,
	prod_cycle text NULL,
	invp_no text NULL,
	invp_area text NULL,
	is_hold bool DEFAULT false NOT NULL,
	wm_qty_rmk01 text NULL,
	wm_qty_rmk02 text NULL,
	wm_qty_rmk03 text NULL,
	wm_qty_rmk04 text NULL,
	wm_qty_rmk05 text NULL,
	wm_qty_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT szjy_wm_inventory_pk PRIMARY KEY (inventory_id)
);
CREATE INDEX szjy_wm_inventory_part_no_idx ON public.szjy_wm_inventory USING btree (part_no);
CREATE INDEX szjy_wm_inventory_part_no_idx_2 ON public.szjy_wm_inventory USING btree (part_no, lot_no);


-- public.wm_temp_receipt definition

-- Drop table

-- DROP TABLE public.wm_temp_receipt;

CREATE TABLE public.wm_temp_receipt (
	temp_receipt_id text DEFAULT af_auid() NOT NULL,
	temp_receipt_status text NULL,
	temp_receipt_no text DEFAULT af_ss_no_generate('wm_temp_receipt_no'::character varying) NOT NULL,
	temp_receipt_type text NULL,
	delivery_no text NULL,
	factory_no text NULL,
	temp_receipt_rmk01 text NULL,
	temp_receipt_rmk02 text NULL,
	temp_receipt_rmk03 text NULL,
	temp_receipt_rmk04 text NULL,
	temp_receipt_rmk05 text NULL,
	temp_receipt_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT wm_temp_receipt_pk PRIMARY KEY (temp_receipt_id)
);


-- public.wm_temp_receipt_b definition

-- Drop table

-- DROP TABLE public.wm_temp_receipt_b;

CREATE TABLE public.wm_temp_receipt_b (
	temp_receipt_b_id text DEFAULT af_auid() NOT NULL,
	temp_receipt_id text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_qty_plan numeric DEFAULT 0 NOT NULL,
	part_qty_real numeric DEFAULT 0 NOT NULL,
	mo_no text NULL,
	temp_receipt_rmk01 text NULL,
	temp_receipt_rmk02 text NULL,
	temp_receipt_rmk03 text NULL,
	temp_receipt_rmk04 text NULL,
	temp_receipt_rmk05 text NULL,
	temp_receipt_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT wm_temp_receipt_b_pk PRIMARY KEY (temp_receipt_b_id)
);

-- public.wm_temp_receipt_sn_part definition

-- Drop table

-- DROP TABLE public.wm_temp_receipt_sn_part;

CREATE TABLE public.wm_temp_receipt_sn_part (
	temp_receipt_sn_id text DEFAULT af_auid() NOT NULL,
	temp_receipt_id text NULL,
	sn_no text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_qty numeric DEFAULT 0 NOT NULL,
	mo_no text NULL,
	temp_receipt_rmk01 text NULL,
	temp_receipt_rmk02 text NULL,
	temp_receipt_rmk03 text NULL,
	temp_receipt_rmk04 text NULL,
	temp_receipt_rmk05 text NULL,
	temp_receipt_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT wm_temp_receipt_sn_part_pk PRIMARY KEY (temp_receipt_sn_id)
);


-- public.me_finish_io_h definition

-- Drop table

-- DROP TABLE public.me_finish_io_h;

CREATE TABLE public.me_finish_io_h (
	me_finish_io_id_h text DEFAULT af_auid() NOT NULL,
	me_finish_io_no text DEFAULT af_ss_no_generate('me_finish_io_no'::character varying) NOT NULL,
	finish_io_status text NULL,
	finish_io_datetime timestamp NOT NULL,
	factory_no text NOT NULL,
	mo_no text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_idt text NULL,
	lot_no text NULL,
	finish_io_qty_ok numeric(18, 4) NULL,
	invp_no_ok text NULL,
	finish_io_qty_ng numeric(18, 4) DEFAULT 0 NULL,
	invp_no_ng text NULL,
	finish_io_qty_scrap numeric(18, 4) DEFAULT 0 NULL,
	invp_no_scrap text NULL,
	finish_io_qty_other numeric(18, 4) DEFAULT 0 NULL,
	invp_no_other text NULL,
	workshop_no text NOT NULL,
	workshop_worker_no text NULL,
	workshop_worker_name text NULL,
	me_finish_io_rmk1 text NULL,
	me_finish_io_rmk2 text NULL,
	me_finish_io_rmk3 text NULL,
	me_finish_io_rmk4 text NULL,
	fb_id text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	io_is_sucessed bool NULL,
	io_times int4 NULL,
	io_last_time timestamp NULL,
	mo_type varchar NULL,
	sap_inbound_apply_no text NULL,
	sap_inbound_no text NULL,
	CONSTRAINT pk_me_finish_io_no_2 PRIMARY KEY (me_finish_io_no)
)
WITH (
	autovacuum_enabled=true
);
CREATE INDEX me_finish_io_h_crt_time_idx ON public.me_finish_io_h USING btree (crt_time);



-- public.me_finish_io_h definition

-- Drop table

-- DROP TABLE public.me_finish_io_h;

CREATE TABLE public.me_finish_io_h (
	me_finish_io_id_h text DEFAULT af_auid() NOT NULL,
	me_finish_io_no text DEFAULT af_ss_no_generate('me_finish_io_no'::character varying) NOT NULL,
	finish_io_status text NULL,
	finish_io_datetime timestamp NOT NULL,
	factory_no text NOT NULL,
	mo_no text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_idt text NULL,
	lot_no text NULL,
	finish_io_qty_ok numeric(18, 4) NULL,
	invp_no_ok text NULL,
	finish_io_qty_ng numeric(18, 4) DEFAULT 0 NULL,
	invp_no_ng text NULL,
	finish_io_qty_scrap numeric(18, 4) DEFAULT 0 NULL,
	invp_no_scrap text NULL,
	finish_io_qty_other numeric(18, 4) DEFAULT 0 NULL,
	invp_no_other text NULL,
	workshop_no text NOT NULL,
	workshop_worker_no text NULL,
	workshop_worker_name text NULL,
	me_finish_io_rmk1 text NULL,
	me_finish_io_rmk2 text NULL,
	me_finish_io_rmk3 text NULL,
	me_finish_io_rmk4 text NULL,
	fb_id text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	io_is_sucessed bool NULL,
	io_times int4 NULL,
	io_last_time timestamp NULL,
	mo_type varchar NULL,
	sap_inbound_apply_no text NULL,
	sap_inbound_no text NULL,
	CONSTRAINT pk_me_finish_io_no_2 PRIMARY KEY (me_finish_io_no)
)
WITH (
	autovacuum_enabled=true
);
CREATE INDEX me_finish_io_h_crt_time_idx ON public.me_finish_io_h USING btree (crt_time);



-- public.qm_si_lot_h definition

-- Drop table

-- DROP TABLE public.qm_si_lot_h;

CREATE TABLE public.qm_si_lot_h (
	si_lot_h_id text DEFAULT af_auid() NOT NULL,
	si_lot_h_no text DEFAULT af_ss_no_generate('si_lot_h_no'::character varying) NOT NULL,
	si_lot_h_status text NOT NULL,
	factory_no text NOT NULL,
	factory_name text NOT NULL,
	part_no text NOT NULL,
	part_name text NULL,
	part_spec text NULL,
	part_idt text NULL,
	wkp_no text NULL,
	wkp_name text NULL,
	si_lot_qty numeric(18, 4) NOT NULL,
	si_lot_move_type text NOT NULL,
	si_lot_move_type_name text NOT NULL,
	move_order_h_id text NULL,
	move_order_b_id text NULL,
	move_order_id text NULL,
	move_order_no text NULL,
	order_type text NULL,
	order_type_name text NULL,
	order_h_id text NULL,
	order_b_id text NULL,
	order_id text NULL,
	order_no text NULL,
	client_no text NULL,
	client_name text NULL,
	supplier_no text NULL,
	supplier_name text NULL,
	si_type text NOT NULL,
	si_type_name text NOT NULL,
	si_degree text NOT NULL,
	si_degree_name text NOT NULL,
	si_level text NOT NULL,
	si_level_name text NOT NULL,
	si_aql text NOT NULL,
	si_lot_qty_ok numeric(18, 4) DEFAULT 0 NOT NULL,
	si_lot_qty_ng numeric(18, 4) DEFAULT 0 NOT NULL,
	si_conclusion_no text NULL,
	si_conclusion_name text NULL,
	si_is_pass bool NULL,
	si_lot_h_rmk01 text NULL,
	si_lot_h_rmk02 text NULL,
	si_lot_h_rmk03 text NULL,
	si_lot_h_rmk04 text NULL,
	si_lot_h_rmk05 text NULL,
	si_lot_h_rmk06 text NULL,
	si_lot_h_rmk07 text NULL,
	si_lot_h_rmk08 text NULL,
	si_lot_h_rmk09 text NULL,
	si_lot_h_rmk10 text NULL,
	da_switch_id text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	io_is_sucessed bool NULL,
	io_times int4 DEFAULT 0 NULL,
	io_last_time timestamp NULL,
	delivery_order_no text NULL,
	si_sample_qty int4 DEFAULT 0 NULL,
	si_lot_h_rmk11 text NULL,
	si_lot_h_rmk12 text NULL,
	si_lot_h_rmk13 text NULL,
	si_lot_h_rmk14 text NULL,
	si_lot_h_rmk15 bool DEFAULT false NULL,
	si_lot_h_rmk16 text NULL,
	si_lot_h_rmk17 timestamp NULL,
	si_lot_h_rmk18 timestamp NULL,
	si_lot_h_rmk19 text NULL,
	si_lot_h_rmk20 text NULL,
	si_lot_h_rmk21 text NULL,
	si_lot_h_rmk22 text NULL,
	si_lot_h_rmk23 bool NULL,
	si_lot_h_rmk24 text NULL,
	si_lot_h_rmk25 text NULL,
	si_lot_h_rmk26 text NULL,
	si_lot_h_rmk27 text NULL,
	si_lot_h_rmk28 text NULL,
	si_lot_h_rmk29 text NULL,
	si_lot_h_rmk30 text DEFAULT '0' NULL,
	si_lot_h_rmk31 text NULL,
	price numeric(18, 6) NULL,
	priceafvat numeric(18, 6) NULL,
	lot_no text NULL,
	put_qty numeric(16, 6) DEFAULT 0 NULL,
	si_lot_h_rmk32 text NULL,
	whscode text NULL,
	canceled text NULL,
	qm_si_gist_no text NULL,
	ea_no text NULL,
	ea_name text NULL,
	si_lot_h_rmk33 text NULL,
	si_lot_h_rmk34 text NULL,
	ng_count text NULL,
	ng_ways text NULL,
	ng_level text NULL,
	is_8d text NULL,
	file_path text NULL,
	ng_ways_name text NULL,
	conclusion_cancel_reason text NULL,
	CONSTRAINT pk_qm_si_lot_h PRIMARY KEY (si_lot_h_id)
)
WITH (
	autovacuum_enabled=true
);
CREATE INDEX qm_si_lot_h_delivery_order_no_idx ON public.qm_si_lot_h USING btree (delivery_order_no);
CREATE INDEX qm_si_lot_h_lot_no_idx ON public.qm_si_lot_h USING btree (lot_no);
CREATE INDEX qm_si_lot_h_part_no_idx ON public.qm_si_lot_h USING btree (part_no);
CREATE INDEX qm_si_lot_h_si_lot_h_no_idx ON public.qm_si_lot_h USING btree (si_lot_h_no);
CREATE INDEX qm_si_lot_h_supplier_name_idx ON public.qm_si_lot_h USING btree (supplier_name);
CREATE INDEX qm_si_lot_h_supplier_no_idx ON public.qm_si_lot_h USING btree (supplier_no);


-- public.cr_so_h definition

-- Drop table

-- DROP TABLE public.cr_so_h;

CREATE TABLE public.cr_so_h (
	so_h_id text DEFAULT af_auid() NOT NULL,
	so_h_no text DEFAULT af_ss_no_generate('so_h_no'::character varying) NOT NULL,
	so_h_status text DEFAULT '210'::text NOT NULL,
	client_no text NOT NULL,
	client_name text NOT NULL,
	client_po_no text NULL,
	so_h_rcv_datetime timestamp DEFAULT now() NULL,
	so_h_tax_rate numeric(18, 4) DEFAULT 0 NULL,
	so_h_currency text DEFAULT 'CNY'::text NULL,
	so_h_currency_name text DEFAULT 'CNY'::text NULL,
	so_h_amount numeric(18, 4) DEFAULT 0 NOT NULL,
	so_priority int4 DEFAULT 500 NULL,
	so_h_rmk1 text NULL,
	so_h_rmk2 text NULL,
	so_h_rmk3 text NULL,
	so_h_rmk4 text NULL,
	so_h_rmk5 text NULL,
	so_h_rmk6 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NOT NULL,
	crt_user_name text NOT NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NOT NULL,
	upd_user_name text NOT NULL,
	upd_host text NOT NULL,
	CONSTRAINT cr_so_h_un UNIQUE (so_h_no),
	CONSTRAINT pk_cr_so_h PRIMARY KEY (so_h_id)
)
WITH (
	autovacuum_enabled=true
);
CREATE UNIQUE INDEX ix_cr_so_h ON public.cr_so_h USING btree (so_h_no);

-- public.cr_dlv_h definition

-- Drop table

-- DROP TABLE public.cr_dlv_h;


CREATE TABLE public.cr_dlv_h (
	cr_dlv_h_id text DEFAULT af_auid() NOT NULL,
	cr_dlv_h_no text DEFAULT af_ss_no_generate('cr_dlv_h_no'::character varying) NOT NULL,
	cr_dlv_h_status text NOT NULL,
	cr_dlv_h_status_name text NULL,
	cr_dlv_datetime timestamp NOT NULL,
	client_no text NULL,
	client_name text NULL,
	so_h_no text NULL,
	cr_dlv_h_amount numeric(18, 4) NULL,
	cr_dlv_h_currency text NULL,
	cr_dlv_h_amount_due_date date NULL,
	cr_dlv_h_rmk1 text NULL,
	cr_dlv_h_rmk2 text NULL,
	cr_dlv_h_rmk3 text NULL,
	cr_dlv_h_rmk4 text NULL,
	cr_dlv_h_rmk5 text NULL,
	cr_dlv_h_rmk6 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	cr_dlv_h_rmk7 text NULL,
	cr_dlv_type text NULL,
	emp_id text NULL,
	sap_bill_no text NULL,
	kh_contacter text NULL,
	cr_shipping_addr text NULL,
	CONSTRAINT dlv_h_no_unique UNIQUE (cr_dlv_h_no),
	CONSTRAINT pk_cr_dlv_h PRIMARY KEY (cr_dlv_h_id)
)
WITH (
	autovacuum_enabled=true
);

-- public.cr_dlv_b definition

-- Drop table

-- DROP TABLE public.cr_dlv_b;

CREATE TABLE public.cr_dlv_b (
	cr_dlv_b_id text DEFAULT af_auid() NOT NULL,
	cr_dlv_h_id text NOT NULL,
	part_no text NOT NULL,
	part_name text NOT NULL,
	part_spec text NULL,
	part_idt text NULL,
	part_unit text NULL,
	cr_dlv_qty_plan numeric(18, 4) NOT NULL,
	cr_dlv_qty numeric(18, 4) DEFAULT 0 NOT NULL,
	unit_price numeric(18, 4) NULL,
	invp_no text NULL,
	cr_dlv_b_rmk1 text NULL,
	cr_dlv_b_rmk2 text NULL,
	cr_dlv_b_rmk3 text NULL,
	cr_dlv_b_rmk4 text NULL,
	cr_dlv_b_rmk5 text NULL,
	cr_dlv_b_rmk6 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	so_b_id text NULL,
	so_h_no text NULL,
	cr_dlv_b_rmk7 text NULL,
	cr_dlv_b_rmk8 text NULL,
	cr_dlv_b_rmk9 text NULL,
	cr_dlv_b_rmk10 text NULL,
	cr_dlv_b_rmk11 text NULL,
	cr_dlv_b_rmk12 text NULL,
	cr_dlv_b_rmk13 text NULL,
	cr_dlv_b_rmk14 text NULL,
	kh_part_no text NULL,
	price numeric DEFAULT 0 NOT NULL,
	CONSTRAINT pk_cr_dlv_b PRIMARY KEY (cr_dlv_b_id)
)
WITH (
	autovacuum_enabled=true
);
CREATE INDEX cr_dlv_b_cr_dlv_h_id_idx ON public.cr_dlv_b USING btree (cr_dlv_h_id);
CREATE INDEX cr_dlv_b_part_no_idx ON public.cr_dlv_b USING btree (part_no);


-- public.cr_dlv_sn_part definition

-- Drop table

-- DROP TABLE public.cr_dlv_sn_part;

CREATE TABLE public.cr_dlv_sn_part (
	cr_dlv_sn_part_id text DEFAULT af_auid() NOT NULL,
	cr_dlv_h_id text NOT NULL,
	sn_no text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_unit_name text NULL,
	part_idt text NULL,
	mrp_region_no text NULL,
	lot_no text NULL,
	invp_no text NOT NULL,
	invp_name text NOT NULL,
	part_qty numeric(18, 4) NOT NULL,
	cr_dlv_sn_part_rmk1 text NULL,
	cr_dlv_sn_part_rmk2 text NULL,
	cr_dlv_sn_part_rmk3 text NULL,
	cr_dlv_sn_part_rmk4 text NULL,
	cr_dlv_sn_part_rmk5 text NULL,
	cr_dlv_sn_part_rmk6 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	CONSTRAINT pk_cr_dlv_sn_part PRIMARY KEY (cr_dlv_sn_part_id)
)
PARTITION BY HASH (cr_dlv_sn_part_id);
CREATE INDEX cr_dlv_sn_part_cr_dlv_h_id_idx ON ONLY public.cr_dlv_sn_part USING btree (cr_dlv_h_id);
CREATE INDEX cr_dlv_sn_part_sn_no_idx ON ONLY public.cr_dlv_sn_part USING btree (sn_no);


-- public.cr_rtn_h definition

-- Drop table

-- DROP TABLE public.cr_rtn_h;

CREATE TABLE public.cr_rtn_h (
	cr_rtn_h_id text DEFAULT af_auid() NOT NULL,
	cr_rtn_h_no text DEFAULT af_ss_no_generate('cr_rtn_h_no'::character varying) NOT NULL,
	cr_rtn_h_status text NULL,
	cr_rtn_h_status_name text NULL,
	cr_rtn_datetime timestamp NOT NULL,
	client_no text NULL,
	client_name text NULL,
	cr_rtn_h_amount numeric(18, 4) NULL,
	cr_rtn_h_currency text NULL,
	cr_rtn_h_amount_due_date date NULL,
	so_h_no text NULL,
	cr_dlv_h_no text NULL,
	cr_rtn_h_rmk1 text NULL,
	cr_rtn_h_rmk2 text NULL,
	cr_rtn_h_rmk3 text NULL,
	cr_rtn_h_rmk4 text NULL,
	cr_rtn_h_rmk5 text NULL,
	cr_rtn_h_rmk6 text NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	sap_bill_no text NULL,
	CONSTRAINT pk_cr_rtn_h PRIMARY KEY (cr_rtn_h_id)
)
WITH (
	autovacuum_enabled=true
);