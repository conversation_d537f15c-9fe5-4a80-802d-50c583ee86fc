### 一、 整体业务流程概述

整个成品出入库流程是一个从生产、质检、入库到仓储、销售、拣货、出库的完整闭环，其中入库环节根据生产来源（本厂 vs 分厂）有两条不同的路径。

1.  **入库流程**:
    *   **生产与送检**: 生产线下线的产品（外箱）通过 `PDA完工送检` 功能（此功能包含**逐个实时校验**和**批量提交生成**两个步骤）进入待检状态，并同步生成`完工入库单`和`出厂检验批`。
    *   **分流处理**:
        *   **本厂产品**: 质检合格后，由仓库通过 `PDA仓库接收入库` 直接扫码入库，库存增加，产品状态变为"在库"。
        *   **分厂/委外产品**: 经过出厂检验后运抵本厂，先执行`暂收`操作，产品进入"暂收库"并变为"暂收"状态。系统自动为这批货物创建`到货检验批`。待二次检验合格后，再通过"暂收转正式"的流程，才最终变为"在库"状态。
    *   **SAP集成**: 生产完工后，首先通过 **`接口_MES-SAP生产入库申请单`** 获取SAP申请号；待产品最终入库完成后，再通过 **`接口_MES-SAP生产入库单`** 将入库事实推送到SAP，完成财务记账。

2.  **出库流程**:
    *   **订单驱动**: 销售订单（来自SAP或MES创建）在系统中生成 `销售发货单`，作为出库作业的指令。
    *   **拣货与出库**: 仓库根据发货单进行 `PDA销售出库拣货`（有序列号）或 `PDA销售出库`（无序列号）。过程中会严格校验库存、批次和FIFO规则。
    *   **库存更新**: 拣货完成后，序列号产品库存被锁定（状态变为"拣货"），无序列号产品库存直接被扣减。
    *   **完成与集成**: 所有产品拣货完成后，发货单状态更新为"发货完成"，并通过 **`接口_MES-SAP销售发货单`** 推送给SAP，完成财务记账和发运流程。

3.  **退货流程**:
    *   **创建退货单**: 销售或客服根据客户退货需求，在系统中创建 `销售退货单`。
    *   **退货入库**: 仓库接收退回的货物，并执行退货入库操作，将货物重新纳入库存管理。
    *   **SAP集成**: 退货入库完成后，通过 **`接口_MES-SAP销售退货单`** 将退货信息推送给SAP，用于生成退货单并进行相应的财务冲销。

### 二、 核心数据库表及关联分析

以下是整个出入库流程中涉及的核心数据库表及其用途。

| 类别 | 表名 | 中文名称 | 主要用途 |
| :--- | :--- | :--- | :--- |
| **主数据** | `pd_part` | 物料主数据表 | 存储物料基础信息，如是否FIFO、序列号管控等。 |
| | `ea` | 设备/产线表 | 存储产线信息，用于生产和入库校验。 |
| | `ss_user` | 用户表 | 记录所有PDA及系统操作员信息。 |
| | `client` | 客户主数据 | 存储客户信息，关联销售发货单。 |
| **生产** | `mo` | 生产订单表 | 存储生产订单信息，是产品追溯的源头。 |
| **仓储/库存** | `wm_sn` | **序列号管理表** | **核心表**，跟踪每个序列号产品的生命周期状态（110-已确认 / 840-暂收 / 800-在库 / 810-拣货 / 900-出库）。 |
| | `szjy_wm_inventory` | **库存表** | **核心表**，存储无序列号产品的库存信息（物料、批次、仓库、数量）。 |
| **入库流程** | `me_finish_io_h` | 生产完工入库主表 | 记录完工入库单的头信息和状态，连接生产与检验。 |
| | `me_finish_io` | 生产完工入库明细表 | 记录完工入库单扫描的序列号等明细。 |
| | `wm_temp_receipt` | **暂收单主表** | **核心表(分厂入库)**，管理分厂/委外到货的暂收操作。 |
| | `wm_temp_receipt_b` | 暂收单明细表 | 按物料汇总暂收的计划与实收数量。 |
| | `wm_temp_receipt_sn_part` | 暂收序列号表 | 记录暂收的每一个产品序列号，用于生成二次检验批。 |
| | `qm_si_lot_h` | **检验批次主表** | **核心表**，存储质检信息和检验结论。根据`si_lot_move_type`区分是出厂检还是到货复检。 |
| **出库流程** |  | 销售订单头表 | (逻辑存在) 在 `cr_dlv_b` 中通过 `so_h_no` 引用, 是销售流程的源头。 |
| | `cr_dlv_h` | **销售发货单主表** | **核心表**，记录销售发货的头信息和状态（待拣货/拣货完成/发货完成），是触发SAP发货接口的关键。 |
| | `cr_dlv_b` | 销售发货单明细表 | 记录发货单需要发运的物料和数量。 |
| | `cr_dlv_sn_part` | **发货序列号/批次表** | **核心表**，记录为发货单拣货/出库的每个序列号或批次数量。 |
| **退货流程** | `cr_rtn_h` | 销售退货单主表 | 记录销售退货的头信息，是触发SAP退货接口的关键。 |
| | `cr_rtn_b` | 销售退货单明细表 | (逻辑存在) 记录退货的物料、数量等详细信息。 |

### 三、 数据流与表关系图

下图清晰地展示了出入库业务中核心表之间的数据流动和关联关系。

```mermaid
graph TD
    subgraph "生产与入库流程"
        MO[("mo<br/>生产订单")] -->|生成| WM_SN_In("wm_sn<br/>序列号 110-确认")
        WM_SN_In -->|完工送检| ME_FINISH_IO_H("me_finish_io_h<br/>完工入库单")
        
        subgraph "SAP 入库集成 (两阶段)"
            direction TB
            ME_FINISH_IO_H --"完工后<br/>rmk4=''"--> SAP_Apply_Doc["接口: 生产入库申请单"]
            SAP_Apply_Doc -->|更新| ME_FINISH_IO_H_Applied("me_finish_io_h<br/>状态: 有申请号")
            ME_FINISH_IO_H_Applied -- "入库后<br/>rmk4='入库完成'" --> SAP_In_Doc["接口: 生产入库单"]
            SAP_In_Doc -->|更新| ME_FINISH_IO_H_Posted("me_finish_io_h<br/>状态: 有入库单号")
        end

        ME_FINISH_IO_H -->|关联| ME_FINISH_IO("me_finish_io<br/>完工入库明细")
        ME_FINISH_IO_H -->|创建| QM_SI_LOT_H("qm_si_lot_h<br/>出厂检验批")
        
        QM_SI_LOT_H -->|检验合格| ME_FINISH_IO_H
        
        subgraph "仓库接收 (按来源分流)"
            direction TB
            ME_FINISH_IO_H -->|本厂产品| Proc_Local_Inbound{"af_pda_wms_prod_factory_inbound<br/>本厂直接入库"}
            ME_FINISH_IO_H -->|分厂/江西产品| Proc_Remote_Temp_Inbound{"af_pda_wms_prod_temp_receipt<br/>分厂暂收"}
        end
        
        Proc_Local_Inbound --> WM_SN_Update("wm_sn<br/>序列号 800-在库")

        Proc_Remote_Temp_Inbound --> WM_TEMP_H("wm_temp_receipt<br/>暂收单")
        Proc_Remote_Temp_Inbound --> WM_SN_Temp("wm_sn<br/>序列号 840-暂收")
        WM_TEMP_H -->|触发二次质检| QM_SI_LOT_H2("qm_si_lot_h<br/>到货检验批")
        QM_SI_LOT_H2 -->|检验合格| Final_Inbound{"正式入库操作<br/>(暂收转入库)"}
        Final_Inbound --> WM_SN_Update
        
        WM_SN_Update -->|非序列号产品| SZJY_WM_INVENTORY_IN("szjy_wm_inventory<br/>库存增加")
    end

    subgraph "仓储核心"
        direction TB
        WM_SN_Storage("wm_sn<br/>序列号-在库")
        SZJY_WM_INVENTORY("szjy_wm_inventory<br/>批次库存")
    end

    subgraph "销售与出库/退货流程"
        SO[("销售订单<br/>(业务源头)")] -->|创建| CR_DLV_H("cr_dlv_h<br/>销售发货单")
        CR_DLV_H -->|关联| CR_DLV_B("cr_dlv_b<br/>发货单明细")
        CR_DLV_B -->|拣货/出库| CR_DLV_SN_PART("cr_dlv_sn_part<br/>发货序列号/批次")
        
        subgraph "库存扣减/增加"
             direction LR
            CR_DLV_SN_PART -->|更新状态| WM_SN_Out("wm_sn<br/>序列号 810-拣货")
            CR_DLV_SN_PART -->|扣减数量| SZJY_WM_INVENTORY_OUT("szjy_wm_inventory<br/>库存减少")
            CR_RTN_H("cr_rtn_h<br/>销售退货单") -->|退货入库| SZJY_WM_INVENTORY_OUT
            CR_RTN_H -->|退货入库| WM_SN_Out
        end
       
        WM_SN_Storage -->|被拣货/退货| CR_DLV_SN_PART
        SZJY_WM_INVENTORY -->|被扣减/退回| CR_DLV_SN_PART

        CR_DLV_H -- "发货完成后" --> SAP_Out_Doc["接口: 销售发货单"]
        CR_RTN_H -- "退货完成后" --> SAP_Return_Doc["接口: 销售退货单"]
    end

    PD_PART[("pd_part<br/>物料主数据")]
    
    MO -- "物料信息" --> PD_PART
    CR_DLV_B -- "物料信息" --> PD_PART
```

### 四、 详细业务逻辑关联

#### 1. 入库业务逻辑流

入库流程以"生产批次"为核心，通过状态和表的串联，实现从生产下线到仓库入库的全程跟踪。

1.  **完工送检 (两阶段流程)**: 这是所有产品入库的第一步，由两个函数协同完成，实现了从实时校验到批量提交的严谨流程。

    *   **阶段一：逐个校验 (`af_jiaoyantiaoma_finish_io`)**
        *   **触发**: 生产线操作员在PDA上**每扫描一个**外箱条码。
        *   **角色**: **前置校验网关**。
        *   **核心逻辑**:
            *   对单个 `sn_no` 进行多维度审查：条码是否存在、状态是否为'110'(已确认)、类型是否为'40'(外箱)、包装是否完成。
            *   校验产线归属，并对重工单('F'开头)有特殊的重复送检逻辑。
            *   检查OQC过程检验中，此条码及箱内码是否被判定为不合格。
            *   校验通过后，仅向PDA返回成功信号，**不写入任何核心业务表**。

    *   **阶段二：批量生成 (`af_me_finish_io_h_scan`)**
        *   **触发**: 操作员完成一批扫描后，在PDA上点击**"提交"**。
        *   **角色**: **后端数据生成器**。
        *   **核心逻辑**:
            1.  接收PDA传来的**整个条码列表**，并存入临时表以提高性能。
            2.  对整批条码进行业务校验（如华为产品的生产周期是否一致）。
            3.  **分组处理**: 以 `(ea_no, mo_no, part_no)` 对条码进行分组，**为每一个分组创建一套独立的单据**。
            4.  **原子性创建**: 在每个分组的循环中，同时向四张核心表写入数据：
                *   `me_finish_io_h` (完工入库单-头)：创建头记录。
                *   `me_finish_io` (完工入库单-明细)：为该组的每个SN创建明细。
                *   `qm_si_lot_h` (品质检验批-头)：创建检验批头，`si_lot_move_type`='310'(完工检验)，并通过 `move_order_no` 字段关联到刚刚生成的 `me_finish_io_no`，**这是打通生产与质检的关键纽带**。
                *   `qm_si_lot_b_sn` (检验批-序列号明细)：将外箱及其包含的所有内包装条码一并写入，建立完整追溯链。

2.  **品质检验 (`AX_生产完工检验`)**:
    *   **触发**: 质检员检验产品。
    *   **核心表**: `qm_si_lot_h`。
    *   **逻辑**: 质检员更新 `qm_si_lot_h` 表中的检验结论 `si_conclusion_name` 为"合格"或"特采"。这是下一步仓库接收的先决条件。

3.  **仓库接收 (区分来源)**: 这是入库流程的关键分水岭，根据生产来源 (`me_finish_io_h.workshop_no`) 调用不同的处理逻辑。

    *   **路径 A：本厂产品直接入库 (`af_pda_wms_prod_factory_inbound`)**
        *   **触发**: 仓库管理员扫描本厂生产的、已检验合格的成品外箱。
        *   **核心逻辑**:
            1.  再次确认质检结论为"合格"或"特采"。
            2.  **一步到位**：直接将 `wm_sn` 表中对应序列号（及其内包装）的状态从 '110' 更新为 **'800' (在库)**，并写入正式库位。
            3.  更新 `me_finish_io_h` 的状态 `me_finish_io_rmk4` 为 **"入库完成"**。
        *   **结果**: 产品直接进入可销售库存。

    *   **路径 B：分厂/委外产品暂收 (`af_pda_wms_prod_temp_receipt`)**
        *   **触发**: 仓库管理员扫描江西等分厂运抵的成品外箱。
        *   **核心逻辑**:
            1.  **创建暂收单**: 在 `wm_temp_receipt` 中创建暂收单来管理本次到货。
            2.  **更新中间状态**: 将 `wm_sn` 序列号状态更新为 **'840' (暂收)**，并放入"暂收库"。此状态的库存**不可销售**。
            3.  **触发二次质检**: 当暂收单收货完成后，系统自动调用 `af_ax_wms_temp_receipt_push_qm_si_lot` 函数。该函数会为暂收单创建一张新的检验批 (`qm_si_lot_h`)，检验类型 `si_lot_move_type` 被明确设置为 **'315' (完工检验(复检))**，并关联所有暂收序列号，等待主厂IQC检验。
            4.  **最终入库**: 待IQC的复检合格后，后续流程会将序列号状态从 '840' (暂收) 正式更新为 '800' (在库)，并移入正式库位。
        *   **结果**: 产品经过两道质检（出厂检+到货检），确保质量后才进入正式库存，流程更严谨。

4.  **SAP集成 (`接口_MES-SAP生产入库单`)**:
    *   **触发**: 后台定时任务。
    *   **核心表**: `me_finish_io_h`。
    *   **逻辑**: 接口程序会抓取状态为 **"入库完成"** 的 `me_finish_io_h` 记录，将其转换为SAP需要的格式，并通过API推送到SAP系统生成财务凭证。

#### 2. 出库业务逻辑流

出库流程以"销售发货单"为驱动，核心是库存的准确扣减和状态的同步更新。

1.  **生成发货单**:
    *   **触发**: SAP的销售订单下推或在MES中手动创建。
    *   **核心表**: `cr_dlv_h`, `cr_dlv_b`。
    *   **逻辑**: 系统生成一张销售发货单，头表 `cr_dlv_h` 的状态 `cr_dlv_h_rmk6` 初始为空（表示待处理）。

2.  **拣货/出库 (`af_pda_wms_sales_outbound_picking_2` / `..._no_sn`)**:
    *   **触发**: 仓库操作员使用PDA扫描发货单号，然后扫描产品条码或按批次出库。
    *   **核心表**: `cr_dlv_h`, `cr_dlv_b`, `cr_dlv_sn_part`, `wm_sn`, `szjy_wm_inventory`。
    *   **逻辑**:
        *   **对于有序列号产品 (拣货)**：
            *   系统校验 `wm_sn` 状态（必须是'800'-在库）和FIFO规则（如果 `pd_part.is_fifo` 为true）。
            *   在 `cr_dlv_sn_part` 中插入一条拣货记录。
            *   将 `wm_sn` 中对应序列号的状态更新为 **'810' (拣货)**，实现库存锁定。
        *   **对于无序列号产品 (出库)**：
            *   系统校验 `szjy_wm_inventory` 中的库存数量是否充足。
            *   按 `lot_no` (批号) 实现先进先出。
            *   在 `cr_dlv_sn_part` 中插入批次出库记录。
            *   直接扣减 `szjy_wm_inventory` 中的库存数量。
        *   同时更新 `cr_dlv_b` 中的已发货数量 `cr_dlv_qty`。

3.  **完成发货**:
    *   **触发**: 当一张发货单下所有 `cr_dlv_b` 明细行的已发货数量 `cr_dlv_qty` 等于计划数量 `cr_dlv_qty_plan` 时。
    *   **核心表**: `cr_dlv_h`, `cr_dlv_b`。
    *   **逻辑**: 系统自动将 `cr_dlv_h` 的状态 `cr_dlv_h_rmk6` 更新为 **"发货完成"**。

4.  **SAP集成 (`接口_MES-SAP销售发货单`)**:
    *   **触发**: 后台定时任务。
    *   **核心表**: `cr_dlv_h`。
    *   **逻辑**: 接口程序抓取状态为 **"发货完成"** 的 `cr_dlv_h` 记录，关联 `cr_dlv_sn_part` 等明细信息，推送给SAP完成发货过账。

#### 3. 退货业务逻辑流

1.  **创建退货单**:
    *   **触发**: 销售或客服根据客户需求创建。
    *   **核心表**: `cr_rtn_h`, `cr_rtn_b`。
    *   **逻辑**: 系统生成一张销售退货单，关联原销售发货单 `cr_dlv_h_no`。

2.  **退货入库**:
    *   **触发**: 仓库人员操作PDA，扫描退回的商品。
    *   **核心表**: `cr_rtn_h`, `wm_sn`, `szjy_wm_inventory`。
    *   **逻辑**:
        *   有序列号产品：更新 `wm_sn` 状态为 '800' (在库)，可能进入待检区。
        *   无序列号产品：增加 `szjy_wm_inventory` 对应批次的库存。
        *   更新退货单状态为 **"退货完成"**。

### 五、 SAP接口集成逻辑详解

#### 1. 生产入库申请单接口 (`接口_MES-SAP生产入库申请单.md`)

*   **核心功能**: 生产完工后，**立即**向SAP系统进行"预申请"，获取一个用于追踪的申请单号。这是正式入库前置步骤。
*   **触发条件**:
    ```sql
    where coalesce(me_finish_io_rmk4,'')='' -- 尚未正式入库
      and coalesce(sap_inbound_apply_no,'')='' -- 尚未申请
    ```
*   **SAP 方法**: `mes-sap-Addon`，表明这是一个对SAP自定义功能的调用。
*   **关键逻辑**:
    *   仅依赖 `me_finish_io_h` 单表数据。
    *   通过 `strpos(mo_no,'F')=1` 判断是否为 **分切单**，构建不同的JSON数据体。分切单会移除'F'前缀，作为原单号传递。
*   **结果**: 成功后，将返回的申请单号更新到 `me_finish_io_h` 表的 `sap_inbound_apply_no` 字段。
*   **错误处理**: 单条失败会记录日志，但不会中断整个批次的处理，容错性较高。

#### 2. 生产入库单接口 (`接口_MES-SAP生产入库单.md`)

*   **核心功能**: 在仓库完成实际的入库操作后，将入库事实正式推送给SAP进行库存和财务的收货记账。
*   **触发条件**:
    ```sql
    where ma.me_finish_io_rmk4='入库完成' -- 已确认入库
      and coalesce(ma.sap_inbound_apply_no,'')!='' -- 必须已有申请号
      and coalesce(ma.sap_inbound_no,'')='' -- 尚未正式过账
    ```
*   **SAP 方法**: `mes-sap-OIGN`，这是SAP B1标准的库存收货接口。
*   **关键逻辑**:
    *   关联 `mo` 表获取会计科目，关联 `pd_part` 表获取库位信息。
    *   同样区分**正常订单**和**分切单**。
    *   正常订单：基于生产订单(`BaseRef`)创建收货。
    *   分切单：直接按物料(`ItemCode`)进行收货。
*   **结果**: 成功后，将返回的SAP物料凭证号更新到 `me_finish_io_h` 表的 `sap_inbound_no` 字段，完成闭环。
*   **错误处理**: 任何失败都会 `throw result.message`，中断执行，确保数据一致性。

#### 3. 销售发货单接口 (`接口_MES-SAP销售发货单.md`)

*   **核心功能**: 在MES中完成发货后，将发货信息推送给SAP，用于从财务和库存层面正式记录销售出库。
*   **触发条件**:
    ```sql
    where cr_dlv_h_rmk6='发货完成' -- 发货流程已在MES结束
      and coalesce(sap_bill_no,'')='' -- 尚未推送SAP
    ```
*   **SAP 方法**: `mes-sap-ODLN`，这是SAP B1标准的销售发货单接口。
*   **关键逻辑**:
    *   使用非常复杂的CTE（WITH AS...）查询，聚合 `cr_dlv_h` (头), `cr_dlv_b` (明细), `cr_dlv_sn_part` (序列号/批次)三张表的数据。
    *   通过 `json_agg` 将同一物料的多个发货批次聚合成一个JSON数组，结构为 `[{"BatchNo": "批号1", "Qty": 数量1}, ...]`，精准处理多批次发货。
    *   通过 `cr_dlv_type` 区分是基于销售订单的发货还是其他类型的发货。
*   **结果**: 成功后，将返回的SAP发货单号更新到 `cr_dlv_h` 表的 `sap_bill_no` 字段。
*   **错误处理**: 失败时中断执行，保证数据不会部分成功。

#### 4. 销售退货单接口 (`接口_MES-SAP销售退货单.md`)

*   **核心功能**: 将MES中的销售退货事实推送到SAP，以创建退货凭证，实现库存和财务的冲销。
*   **触发条件**: (推测)
    ```sql
    where cr_rtn_h_status='退货完成' -- 退货流程已在MES结束
      and coalesce(sap_bill_no,'')='' -- 尚未推送SAP
    ```
*   **SAP 方法**: `mes-sap-ORDN`，这是SAP B1标准的销售退货单接口。
*   **关键逻辑**:
    
    *   SQL部分缺失，但可推断其会关联 `cr_rtn_h` (退货头) 和 `cr_rtn_b` (退货明细) 以及原始的 `cr_dlv_h` (发货单)来构建数据。
*   **结果与问题**: 成功后，更新 `cr_rtn_h` 的 `sap_bill_no` 字段。
    *   **⚠️ 潜在问题**: 在提供的代码中，成功后更新 `sap_bill_no` 的语句被**注释掉了**：
        ```javascript
        /*ReadContext.ExecuteSql("update cr_rtn_h set sap_bill_no=...");*/
        ```
        这意味着即使SAP成功创建了退货单，MES中的状态也不会更新，**极有可能导致重复推送**，需要立即审查和修复。
*   **错误处理**: 失败时中断执行。



