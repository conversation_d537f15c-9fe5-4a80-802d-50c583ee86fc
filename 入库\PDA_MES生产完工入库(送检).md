```sql
-- DROP FUNCTION public.af_jiaoyantiaoma_finish_io(varchar);

CREATE OR REPLACE FUNCTION public.af_jiaoyantiaoma_finish_io(datas character varying)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$  
/*
---扫描前校验   select af_sales_notice_bf('k')  af_jiaoyantiaoma 
*/		 

declare
    _datas json;
     _results returntype; 
    _sn_no varchar;  
    _re_datas varchar; 
    _mo_no text;
    _mo_no_f text;
    _ea_no text;
    _mo_parent_top text;
    _mo_ea_no text;
    _sn_status_name text;
    _sn_type text;
    _sn_type_name text;

	_part_no_sn text;
	_part_no_mo text;
   
   	_si_lot_h_id	text;
   	_si_lot_h_no	text;
   	_s_sn_no	text;
    tmp	json;
   
begin  
  		--- 
	 _datas:=json(datas); 
	-- _re_datas:=(_datas->'datas')::varchar; 
    _sn_no:=_datas->'datas'->0->>'sn_no';
   _ea_no:=_datas->'datas'->0->>'ea_no';
    raise notice '%',_sn_no;
   
  -- insert into a_test_log
  --	 select datas,'af_jiaoyantiaoma_finish_io',now();
		if (coalesce(_sn_no,'') = '') then 
    	_results:=row('false','外箱条码不能为空！');
   		return  to_json(_results);  
    end if; 
   
   	select ws.mo_no,part_no into _mo_no,_part_no_sn from wm_sn ws where ws.sn_no =_sn_no ;
    
    select m.workshop_no,m.mo_parent_top,part_no into _mo_ea_no,_mo_parent_top,_part_no_mo  from mo m where m.mo_no = _mo_no;
	if coalesce(_part_no_sn,'part_no_sn')<>coalesce(_part_no_mo,'part_no_mo') then
		_results := row('false','标签的物料编码与工单的物料编码不一致。');
		return to_json(_results);
	end if;
		
		if not exists(select 1 from wm_sn where sn_no=_sn_no)   then 
    	_results:=row('false','外箱条码:'||_sn_no||'不存在！');
   		return  to_json(_results);  
    end if; 
   
    if not exists(select 1 from wm_sn where sn_no=_sn_no and wm_sn.sn_type ='40'  )   then 
			select sn_type,sn_type_name from into _sn_type,_sn_type_name wm_sn where sn_no=_sn_no;
    	_results:=row('false','条码类型:['||_sn_type|| '-' || _sn_type_name ||']不是外箱类型！');
   		return  to_json(_results);  
    end if; 
   
	if not exists(select 1 from wm_sn where sn_no=_sn_no and sn_status='110') then
		select sn_status_name into _sn_status_name from wm_sn where sn_no=_sn_no;
		_results := row('false', '外箱条码状态:【'||_sn_status_name||'】不是确认状态(110),不允许完工送检');
		return to_json(_results);
	end if;
   --判断外箱条码是否已包装完成  pack_qty_used
   	if exists(select 1 from wm_sn where sn_no=_sn_no and coalesce (wm_sn.pack_qty_used,0) <> wm_sn.part_qty )   then 
    	_results:=row('false','外箱条码:'||_sn_no||'未包装完成，不允许完工送检！');
   		return  to_json(_results);  
    end if; 
   
    --判断是否江西产线2023-05-31 增加委外分厂产线校验
   	if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%')then
   		if not exists(select 1 from mo_rmb b
						left join mo_rmb_ea e on e.unode = b.unode 
						left join ea a on a.ea_id = e.ea_id 
						where b.mo_no = _mo_no and a.ea_no = _ea_no)then
			_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);
   		end if;
   	else
		--条码工单对应的产线编号与参数产线是否一致验证；
	    if  _ea_no <> coalesce(_mo_ea_no,'_mo_ea_no') then 
	    	_results:=row('false','生产订单：'||_mo_no||'产线编号与入库产线编号不一致，不允许完工送检扫描！');
	   		return  to_json(_results);  
	    end if; 
   	end if;
   
    
   
   
   /* if not exists(select 1 from mo m where m.mo_no =_mo_no and m.mo_status in ('300','310') )   then 
    	_results:=row('false','生产订单：'||_mo_no||'不存在或状态已结案！');
   		return  to_json(_results);  
    end if; */
   	--判断条码是否存在送检记录
    if  exists(select 1 	from me_finish_io a ,me_finish_io_h h 
   				where a.me_finish_io_no = h.me_finish_io_no  
   				and a.sn_no = _sn_no and h.finish_io_status<>'230')   then 
   				--2023-05-17判断是否重工单
   				_mo_no_f := substring(_mo_no,1,1);
   				if(_mo_no_f = 'F')then
				   		--重工单校验：重工单+条码是否存在送检记录
				   		if exists(select 1 	from me_finish_io a ,me_finish_io_h h 
				   				where a.me_finish_io_no = h.me_finish_io_no  
				   				and a.sn_no = _sn_no and h.finish_io_status<>'230' and a.mo_no = _mo_no)then
				   				_results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
								return  to_json(_results);
				   		end if;
				 else
				        _results:=row('false','条码'||_sn_no||'已完工送检扫描，不能再次送检扫描！');
						return  to_json(_results);
				 end if;
		    	  
   	end if; 
   
   
   --判断条码是否在过程检验判定不合格2023-08-03
   if exists(select 1 from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格')then
   		select si_lot_h_id ,si_lot_h_no into _si_lot_h_id,_si_lot_h_no from qm_si_lot_h h where h.order_no = _mo_no and h.si_lot_move_type = '303' and h.si_conclusion_name = '不合格';
   		if exists(select 1 from szjy_oqc_gc_disqualified d where d.si_lot_h_no = _si_lot_h_no and d.mo_no = _mo_no and d.disqualified_si_conclusion_name = '批退')then
   			--获取外箱绑定的条码
   			select array_to_json(array_agg(row_to_json(t))) into tmp
   			from(
   				select s.sn_no from wm_sn s where s.sn_pack_50  = _sn_no and s.sn_type = '00'
   			)t;
   			--循环外箱绑定的产品条码
   			for icount in 0..json_array_length(tmp)-1 loop
	   			_s_sn_no :=tmp->icount->>'sn_no';
   				if exists(select 1 from qm_si_lot_b_sn b where b.si_lot_h_id = _si_lot_h_id and b.sn_no = _s_sn_no)then
   					_results:=row('false','产品条码'||_s_sn_no||' 在OQC过程检验判定为不合格，不能进行完工入库送检，请确认！');
					return  to_json(_results);
   				end if;
   			end loop;
   			
   		end if;
   		
   end if;
   
   

   _results:=row('true','扫码成功','[{"sn_no":"'||_sn_no||'"}]');
	return  to_json(_results);  
	END;
$function$
;

```

逻辑分析：



          
## PDA MES生产完工入库(送检)功能分析

基于 <mcfile name="PDA_MES生产完工入库(送检).md" path="d:\金洋\成品出入库记录\入库\PDA_MES生产完工入库(送检).md"></mcfile> 文件中的PostgreSQL函数，我来分析这个功能的代码逻辑、业务逻辑和数据逻辑：

### 功能概述
函数 `af_jiaoyantiaoma_finish_io` 是生产完工入库送检的核心校验函数，主要用于扫描外箱条码进行完工送检前的各项校验。

### 代码逻辑分析

#### 1. 输入参数处理
```sql
_datas := json(datas);
_sn_no := _datas->'datas'->0->>'sn_no';  -- 外箱条码
_ea_no := _datas->'datas'->0->>'ea_no';  -- 产线编号
```

#### 2. 校验流程（按执行顺序）
1. **基础参数校验**：外箱条码不能为空
2. **条码存在性校验**：验证外箱条码是否存在
3. **条码类型校验**：必须是外箱类型(sn_type='40')
4. **条码状态校验**：必须是确认状态(sn_status='110')
5. **包装完成校验**：验证外箱是否包装完成
6. **产线一致性校验**：验证工单产线与入库产线是否一致
7. **重复送检校验**：防止重复送检扫描
8. **过程检验校验**：检查是否存在不合格的过程检验

#### 3. 返回结果处理
```sql
_results := row('true','扫码成功','[{"sn_no":"'||_sn_no||'"}]');
return to_json(_results);
```

### 业务逻辑分析

#### 1. 外箱条码管理
- **条码类型控制**：只允许外箱类型条码(sn_type='40')进行送检
- **状态管理**：条码必须处于确认状态才能送检
- **包装完成验证**：确保外箱已完成包装(`pack_qty_used = part_qty`)

#### 2. 产线管控逻辑
```sql
-- 江西产线特殊处理
if exists(select 1 from ea where ea_no = _ea_no and ea_type3 like '%江西%') then
    -- 验证工单与产线的关联关系
else
    -- 常规产线验证
end if;
```

#### 3. 重工单特殊处理
```sql
_mo_no_f := substring(_mo_no,1,1);
if(_mo_no_f = 'F') then  -- 重工单以F开头
    -- 重工单允许同一条码在不同工单下重新送检
else
    -- 普通工单不允许重复送检
end if;
```

#### 4. 质量管控
- **过程检验校验**：检查OQC过程检验是否判定为不合格
- **批退处理**：对于批退的不合格品，逐一检查外箱内的产品条码

### 数据逻辑分析

#### 1. 核心数据表
- **wm_sn**：条码主表
  - `sn_no`：条码号
  - `sn_type`：条码类型('40'=外箱)
  - `sn_status`：条码状态('110'=确认)
  - `pack_qty_used`：已包装数量
  - `part_qty`：计划包装数量
  - `sn_pack_50`：外箱绑定关系

- **mo**：生产订单表
  - `mo_no`：工单号
  - `workshop_no`：车间编号
  - `mo_parent_top`：顶层工单
  - `part_no`：物料编号

- **me_finish_io/me_finish_io_h**：完工入库表
  - `me_finish_io_no`：完工入库单号
  - `finish_io_status`：入库状态
  - `sn_no`：条码号

- **qm_si_lot_h/qm_si_lot_b_sn**：检验批次表
  - `si_lot_move_type`：检验类型('303'=过程检验)
  - `si_conclusion_name`：检验结论

#### 2. 数据验证逻辑
```sql
-- 物料编码一致性验证
if coalesce(_part_no_sn,'') <> coalesce(_part_no_mo,'') then
    -- 标签物料编码与工单物料编码不一致
end if;
```

#### 3. 关联关系验证
- **条码与工单关联**：通过wm_sn表关联
- **工单与产线关联**：通过mo_rmb_ea表关联
- **外箱与产品条码关联**：通过sn_pack_50字段关联

### 关键业务规则

#### 1. 条码管控规则
- 外箱条码必须是确认状态且包装完成
- 不允许重复送检（重工单除外）
- 条码类型必须匹配业务场景

#### 2. 产线管控规则
- 江西产线采用特殊的关联验证逻辑
- 工单产线与入库产线必须一致
- 委外分厂产线需要额外校验

#### 3. 质量管控规则
- 过程检验不合格的产品不能送检
- 批退产品需要逐一验证外箱内的产品条码
- 重工单允许重新送检

#### 4. 异常处理
- 每个校验环节都有明确的错误提示
- 使用统一的返回格式
- 提供详细的错误信息便于问题定位

### 总结
这个函数实现了生产完工送检的全面校验，涵盖了条码管理、产线管控、质量管控等多个维度，体现了严格的生产质量管理要求。通过多层次的校验确保只有符合条件的产品才能进入送检流程，有效防止了质量风险和操作错误。
        